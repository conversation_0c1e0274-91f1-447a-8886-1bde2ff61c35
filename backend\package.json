{"name": "syra-security-backend", "version": "1.0.0", "description": "Backend API for SYRA Security Mobile App", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["security", "api", "mobile", "syra"], "author": "SYRA Security Team", "license": "MIT", "dependencies": {"cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.21.2", "helmet": "^7.2.0", "morgan": "^1.10.0", "uuid": "^9.0.1"}, "devDependencies": {"nodemon": "^3.1.10"}}