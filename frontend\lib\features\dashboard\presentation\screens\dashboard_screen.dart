import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:syra_security/config/theme.dart';
import 'package:syra_security/features/dashboard/presentation/widgets/attack_stats_widget.dart';
import 'package:syra_security/features/dashboard/presentation/widgets/domain_card.dart';
import 'package:syra_security/features/domains/data/models/domain_model.dart';
import 'package:syra_security/features/domains/presentation/providers/domain_provider.dart';

class DashboardScreen extends ConsumerStatefulWidget {
  const DashboardScreen({super.key});

  @override
  ConsumerState<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends ConsumerState<DashboardScreen> {
  @override
  void initState() {
    super.initState();
    // Load domains when dashboard is first opened
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(domainListProvider.notifier).loadDomains();
    });
  }

  @override
  Widget build(BuildContext context) {
    debugPrint('DashboardScreen build dipanggil');
    final domainListState = ref.watch(domainListProvider);
    final authState = ref.watch(authProvider);

    // Calculate total attacks from all domains
    final totalAttacks = AttackStats(
      ddos: domainListState.domains.fold(0, (sum, domain) => sum + domain.attackStats.ddos),
      sqlInjection: domainListState.domains.fold(0, (sum, domain) => sum + domain.attackStats.sqlInjection),
      xss: domainListState.domains.fold(0, (sum, domain) => sum + domain.attackStats.xss),
    );

    return Scaffold(
      appBar: AppBar(
        title: const Text('Dashboard'),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () {
              debugPrint('Tombol Add Domain di AppBar diklik');
              try {
                context.go('/addDomain');
                debugPrint('Navigasi ke /addDomain berhasil');
              } catch (e, stackTrace) {
                debugPrint('ERROR navigasi ke /addDomain: $e');
                debugPrint('Stack trace: $stackTrace');
              }
            },
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () async {
              try {
                await ref.read(domainListProvider.notifier).loadDomains();
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Data berhasil diperbarui'),
                      backgroundColor: Colors.green,
                      duration: Duration(seconds: 2),
                    ),
                  );
                }
              } catch (e) {
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Gagal memperbarui data: $e'),
                      backgroundColor: Colors.red,
                      duration: const Duration(seconds: 3),
                    ),
                  );
                }
              }
            },
          ),
          IconButton(
            icon: const Icon(Icons.logout),
            tooltip: 'Logout',
            onPressed: () {
              showDialog(
                context: context,
                builder: (context) => AlertDialog(
                  title: const Text('Logout'),
                  content: Text('Apakah Anda yakin ingin keluar dari akun ${authState.userEmail ?? 'User'}?'),
                  actions: [
                    TextButton(
                      onPressed: () => Navigator.of(context).pop(),
                      child: const Text('Batal'),
                    ),
                    ElevatedButton(
                      onPressed: () {
                        Navigator.of(context).pop();
                        debugPrint('Logout dikonfirmasi');
                        try {
                          ref.read(authProvider.notifier).signOut();
                          debugPrint('SignOut berhasil dipanggil');
                          context.go('/login');
                          debugPrint('Navigasi ke /login berhasil');
                          
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text('Berhasil logout'),
                              backgroundColor: Colors.green,
                              duration: Duration(seconds: 2),
                            ),
                          );
                        } catch (e, stackTrace) {
                          debugPrint('ERROR saat logout: $e');
                          debugPrint('Stack trace: $stackTrace');
                          
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text('Gagal logout: $e'),
                              backgroundColor: Colors.red,
                              duration: const Duration(seconds: 3),
                            ),
                          );
                        }
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.red,
                        foregroundColor: Colors.white,
                      ),
                      child: const Text('Logout'),
                    ),
                  ],
                ),
              );
            },
          ),
        ],
      ),
      body: domainListState.isLoading
          ? const Center(child: CircularProgressIndicator())
          : domainListState.error != null
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(Icons.error, size: 64, color: Colors.red),
                      const SizedBox(height: 16),
                      Text('Error: ${domainListState.error}'),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: () async {
                          try {
                            await ref.read(domainListProvider.notifier).loadDomains();
                          } catch (e) {
                            if (context.mounted) {
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(
                                  content: Text('Masih gagal memuat data: $e'),
                                  backgroundColor: Colors.red,
                                  duration: const Duration(seconds: 3),
                                ),
                              );
                            }
                          }
                        },
                        child: const Text('Retry'),
                      ),
                    ],
                  ),
                )
              : RefreshIndicator(
                  onRefresh: () => ref.read(domainListProvider.notifier).loadDomains(),
                  child: SingleChildScrollView(
                    physics: const AlwaysScrollableScrollPhysics(),
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'Total Number of Attacks Detected',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              
              // Attack Stats
              AttackStatsWidget(stats: totalAttacks),
                          const SizedBox(height: 24),
                          
                          // Domain list
                          const Text(
                            'My Domain',
                            style: TextStyle(
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          domainListState.domains.isEmpty
                              ? Center(
                                  child: Column(
                                    children: [
                                      const SizedBox(height: 32),
                                      const Icon(Icons.domain_disabled, size: 64, color: Colors.grey),
                                      const SizedBox(height: 16),
                                      const Text(
                                        'No domains added yet',
                                        style: TextStyle(fontSize: 18, color: Colors.grey),
                                      ),
                                      const SizedBox(height: 8),
                                      const Text(
                                        'Add your first domain to start monitoring',
                                        style: TextStyle(color: Colors.grey),
                                      ),
                                      const SizedBox(height: 16),
                                      ElevatedButton.icon(
                                        onPressed: () {
                                          debugPrint('Tombol Add Domain di empty state diklik');
                                          try {
                                            context.go('/addDomain');
                                            debugPrint('Navigasi ke /addDomain dari empty state berhasil');
                                          } catch (e, stackTrace) {
                                            debugPrint('ERROR navigasi ke /addDomain dari empty state: $e');
                                            debugPrint('Stack trace: $stackTrace');
                                          }
                                        },
                                        icon: const Icon(Icons.add),
                                        label: const Text('Add Domain'),
                                      ),
                                    ],
                                  ),
                                )
                              : ListView.builder(
                                  shrinkWrap: true,
                                  physics: const NeverScrollableScrollPhysics(),
                                  itemCount: domainListState.domains.length,
                                  itemBuilder: (context, index) {
                                    return Padding(
                                      padding: const EdgeInsets.only(bottom: 16.0),
                                      child: DomainCard(domain: domainListState.domains[index]),
                                    );
                                  },
                                ),
                        ],
                      ),
                    ),
                  ),
                ),
      bottomNavigationBar: Padding(
        padding: const EdgeInsets.all(16.0),
        child: ElevatedButton(
          onPressed: () {
            debugPrint('Tombol Add New Domain diklik, mencoba navigasi ke /addDomain');
            try {
              context.go('/addDomain');
              debugPrint('Navigasi ke /addDomain berhasil dipanggil');
            } catch (e, stackTrace) {
              debugPrint('ERROR saat navigasi: $e');
              debugPrint('Stack trace: $stackTrace');
            }
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: AppTheme.secondaryColor,
            foregroundColor: Colors.white,
          ),
          child: const Text('Add New Domain'),
        ),
      ),
    );
  }
}