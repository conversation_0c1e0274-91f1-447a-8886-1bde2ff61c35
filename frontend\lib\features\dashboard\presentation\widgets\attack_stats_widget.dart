import 'package:flutter/material.dart';
import 'package:syra_security/config/theme.dart';
import 'package:syra_security/features/domains/data/models/domain_model.dart';

class AttackStatsWidget extends StatelessWidget {
  final AttackStats stats;

  const AttackStatsWidget({
    super.key,
    required this.stats,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: _buildStatCard(
            context: context,
            count: stats.ddos,
            label: 'DDoS',
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: _buildStatCard(
            context: context,
            count: stats.sqlInjection,
            label: 'SQL Injection',
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: _buildStatCard(
            context: context,
            count: stats.xss,
            label: 'XSS',
          ),
        ),
      ],
    );
  }

  Widget _buildStatCard({
    required BuildContext context,
    required int count,
    required String label,
  }) {
    return Card(
      elevation: 1,
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              count.toString(),
              style: const TextStyle(
                fontSize: 28,
                fontWeight: FontWeight.bold,
                color: AppTheme.textDark,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              label,
              style: const TextStyle(
                fontSize: 14,
                color: AppTheme.textLight,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}