import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:syra_security/config/theme.dart';
import 'package:syra_security/features/domains/data/models/domain_model.dart';

class SecurityLogTab extends StatelessWidget {
  final List<SecurityLog> logs;

  const SecurityLogTab({
    super.key,
    required this.logs,
  });

  @override
  Widget build(BuildContext context) {
    return logs.isEmpty
        ? const Center(
            child: Text('No security logs available'),
          )
        : ListView.separated(
            padding: const EdgeInsets.all(16),
            itemCount: logs.length,
            separatorBuilder: (context, index) => const SizedBox(height: 4),
            itemBuilder: (context, index) {
              final log = logs[index];
              return SecurityLogItem(log: log);
            },
          );
  }
}

class SecurityLogItem extends StatelessWidget {
  final SecurityLog log;

  const SecurityLogItem({
    super.key,
    required this.log,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            child: RichText(
              text: TextSpan(
                children: [
                  TextSpan(
                    text: '[${DateFormat('yyyy-MM-dd HH:mm:ss').format(log.timestamp)}] ',
                    style: TextStyle(
                      fontFamily: 'Poppins',
                      fontSize: 14,
                      color: log.isAlert ? AppTheme.errorColor : AppTheme.textLight,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  TextSpan(
                    text: log.message,
                    style: TextStyle(
                      fontFamily: 'Poppins',
                      fontSize: 14,
                      color: log.isAlert ? AppTheme.errorColor : AppTheme.textDark,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}