import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:syra_security/features/domains/presentation/providers/domain_provider.dart';

class AddDomainScreen extends ConsumerStatefulWidget {
  const AddDomainScreen({super.key});

  @override
  ConsumerState<AddDomainScreen> createState() => _AddDomainScreenState();
}

class _AddDomainScreenState extends ConsumerState<AddDomainScreen> {
  final _formKey = GlobalKey<FormState>();
  final _urlController = TextEditingController();
  bool _isLoading = false;

  @override
  void dispose() {
    _urlController.dispose();
    super.dispose();
  }

  // Fungsi untuk kembali ke dashboard
  void _navigateToDashboard() {
    debugPrint('Navigating to dashboard from add domain screen');
    context.go('/dashboard');
  }


  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false, // Prevent default back navigation
      onPopInvokedWithResult: (didPop, result) {
        if (!_isLoading) {
          _navigateToDashboard();
        }
      },
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Add New Domain'),
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: _isLoading ? null : _navigateToDashboard,
          ),
        ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Add New Domain Form:',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 24),
              
              // Domain link field
              const Text(
                'Domain Link',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 8),
              TextFormField(
                controller: _urlController,
                decoration: const InputDecoration(
                  hintText: 'E.g. trenteknologimobile.com',
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Silakan masukkan domain';
                  }
                  
                  // Clean the input - remove protocol and trailing slash
                  String cleanDomain = value.trim();
                  cleanDomain = cleanDomain.replaceAll(RegExp(r'^https?://'), '');
                  cleanDomain = cleanDomain.replaceAll(RegExp(r'/$'), '');
                  
                  // Simple domain validation - allow more flexible formats
                  final domainRegex = RegExp(r'^[a-zA-Z0-9][a-zA-Z0-9.-]*[a-zA-Z0-9]\.[a-zA-Z]{2,}$');
                  if (!domainRegex.hasMatch(cleanDomain)) {
                    return 'Silakan masukkan domain yang valid (contoh: example.com)';
                  }
                  return null;
                },
                enabled: !_isLoading,
              ),
              
              const Spacer(),
              
              // Action buttons - Match UI design
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: _isLoading ? null : _navigateToDashboard,
                      style: OutlinedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        side: const BorderSide(color: Colors.grey),
                      ),
                      child: const Text(
                        'Cancel',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: _isLoading
                          ? null
                          : () async {
                              if (_formKey.currentState!.validate()) {
                                setState(() {
                                  _isLoading = true;
                                });

                                try {
                                  // Clean the domain input
                                  String cleanDomain = _urlController.text.trim();
                                  cleanDomain = cleanDomain.replaceAll(RegExp(r'^https?://'), '');
                                  cleanDomain = cleanDomain.replaceAll(RegExp(r'/$'), '');

                                  debugPrint('Adding domain: $cleanDomain');

                                  // Tambahkan domain baru
                                  await ref.read(domainListProvider.notifier).addDomain(cleanDomain);

                                  debugPrint('Domain added successfully');

                                  // Verifikasi domain telah ditambahkan
                                  final domains = ref.read(domainListProvider).domains;
                                  final isDomainAdded = domains.any((domain) => domain.url == cleanDomain);

                                  debugPrint('Domain verification: ${isDomainAdded ? "BERHASIL" : "GAGAL"} ditambahkan');
                                  debugPrint('Total domains setelah penambahan: ${domains.length}');

                                  if (mounted && context.mounted) {
                                    ScaffoldMessenger.of(context).showSnackBar(
                                      SnackBar(
                                        content: Text(
                                          isDomainAdded
                                            ? 'Domain $cleanDomain berhasil ditambahkan!'
                                            : 'Domain ditambahkan tapi perlu refresh',
                                        ),
                                        backgroundColor: isDomainAdded ? Colors.green : Colors.orange,
                                        duration: const Duration(seconds: 3),
                                        action: SnackBarAction(
                                          label: 'OK',
                                          textColor: Colors.white,
                                          onPressed: () {},
                                        ),
                                      ),
                                    );

                                    // Navigate back to dashboard
                                    debugPrint('Navigating back to dashboard after adding domain');
                                    _navigateToDashboard();
                                  }
                                } catch (e) {
                                  debugPrint('Error adding domain: $e');
                                  if (mounted && context.mounted) {
                                    ScaffoldMessenger.of(context)
                                        .showSnackBar(
                                      SnackBar(
                                        content: Text(
                                            'Gagal menambahkan domain: $e'),
                                        backgroundColor: Colors.red,
                                      ),
                                    );
                                  }
                                } finally {
                                  if (mounted) {
                                    setState(() {
                                      _isLoading = false;
                                    });
                                  }
                                }
                              }
                            },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFF1976D2), // Blue color from UI
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: _isLoading
                          ? const SizedBox(
                              height: 20,
                              width: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                color: Colors.white,
                              ),
                            )
                          : const Text(
                              'Save',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
      ),
    );
  }
}