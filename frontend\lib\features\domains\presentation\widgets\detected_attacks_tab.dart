import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:syra_security/config/theme.dart';
import 'package:syra_security/features/domains/data/models/domain_model.dart';

class DetectedAttacksTab extends StatelessWidget {
  final List<DetectedAttack> attacks;

  const DetectedAttacksTab({
    super.key,
    required this.attacks,
  });

  @override
  Widget build(BuildContext context) {
    return attacks.isEmpty
        ? const Center(
            child: Text('No attacks detected'),
          )
        : ListView.separated(
            padding: const EdgeInsets.all(16),
            itemCount: attacks.length,
            separatorBuilder: (context, index) => const SizedBox(height: 16),
            itemBuilder: (context, index) {
              final attack = attacks[index];
              return AttackDetailCard(attack: attack);
            },
          );
  }
}

class AttackDetailCard extends StatelessWidget {
  final DetectedAttack attack;

  const AttackDetailCard({
    super.key,
    required this.attack,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 1,
      margin: EdgeInsets.zero,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '${_getAttackTypeLabel(attack.type)} Detected',
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            _buildInfoRow('Mitigation Status', attack.status),
            _buildInfoRow('Severity', attack.severity),
            _buildInfoRow('Detected At', 
              DateFormat('yyyy-MM-dd HH:mm:ss').format(attack.detectedAt)),
            if (attack.resolvedAt != null)
              _buildInfoRow('Resolved At', 
                DateFormat('yyyy-MM-dd HH:mm:ss').format(attack.resolvedAt!)),
            
            const SizedBox(height: 8),
            const Text(
              'Mitigation Details',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppTheme.backgroundLight,
                border: Border.all(color: AppTheme.dividerColor),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Text(
                attack.details,
                style: const TextStyle(
                  fontSize: 14,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: Text(
              label,
              style: const TextStyle(
                fontSize: 14,
                color: AppTheme.textLight,
              ),
            ),
          ),
          Expanded(
            flex: 3,
            child: Text(
              value,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: value == 'Resolved' 
                    ? AppTheme.successColor
                    : AppTheme.textDark,
              ),
              textAlign: TextAlign.right,
            ),
          ),
        ],
      ),
    );
  }
  
  String _getAttackTypeLabel(AttackType type) {
    switch (type) {
      case AttackType.ddos:
        return 'DDoS';
      case AttackType.sqlInjection:
        return 'SQL Injection';
      case AttackType.xss:
        return 'XSS';
    }
  }
}