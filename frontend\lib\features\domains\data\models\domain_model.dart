import 'package:equatable/equatable.dart';

/// <PERSON><PERSON> serangan yang dapat dideteksi
enum AttackType {
  ddos,
  sqlInjection,
  xss,
}

/// Ekstensi untuk AttackType untuk memudahkan konversi ke string yang lebih mudah dibaca
extension AttackTypeExtension on AttackType {
  String get displayName {
    switch (this) {
      case AttackType.ddos:
        return 'DDoS Attack';
      case AttackType.sqlInjection:
        return 'SQL Injection';
      case AttackType.xss:
        return 'Cross-Site Scripting (XSS)';
    }
  }
  
  String get description {
    switch (this) {
      case AttackType.ddos:
        return 'Distributed Denial of Service attack that attempts to overwhelm server resources';
      case AttackType.sqlInjection:
        return 'Injection of malicious SQL code to manipulate database queries';
      case AttackType.xss:
        return 'Injection of malicious scripts into web pages viewed by other users';
    }
  }
}

/// Tingkat keparahan serangan
enum AttackSeverity {
  low,
  medium,
  high,
  critical,
}

/// Ekstensi untuk AttackSeverity untuk memudahkan konversi ke string yang lebih mudah dibaca
extension AttackSeverityExtension on String {
  AttackSeverity toAttackSeverity() {
    switch (toLowerCase()) {
      case 'low':
        return AttackSeverity.low;
      case 'medium':
        return AttackSeverity.medium;
      case 'high':
        return AttackSeverity.high;
      case 'critical':
        return AttackSeverity.critical;
      default:
        return AttackSeverity.medium;
    }
  }
}

/// Status serangan
enum AttackStatus {
  detected,
  investigating,
  mitigating,
  resolved,
}

/// Ekstensi untuk AttackStatus untuk memudahkan konversi ke string yang lebih mudah dibaca
extension AttackStatusExtension on String {
  AttackStatus toAttackStatus() {
    switch (toLowerCase()) {
      case 'detected':
        return AttackStatus.detected;
      case 'investigating':
        return AttackStatus.investigating;
      case 'mitigating':
        return AttackStatus.mitigating;
      case 'resolved':
        return AttackStatus.resolved;
      default:
        return AttackStatus.detected;
    }
  }
}

/// Statistik serangan untuk domain
class AttackStats extends Equatable {
  final int ddos;
  final int sqlInjection;
  final int xss;

  const AttackStats({
    required this.ddos,
    required this.sqlInjection,
    required this.xss,
  });
  
  /// Total semua serangan
  int get total => ddos + sqlInjection + xss;
  
  /// Membuat salinan dengan nilai yang diperbarui
  AttackStats copyWith({
    int? ddos,
    int? sqlInjection,
    int? xss,
  }) {
    return AttackStats(
      ddos: ddos ?? this.ddos,
      sqlInjection: sqlInjection ?? this.sqlInjection,
      xss: xss ?? this.xss,
    );
  }
  
  /// Menggabungkan statistik dari beberapa domain
  static AttackStats combine(List<AttackStats> statsList) {
    return statsList.fold(
      const AttackStats(ddos: 0, sqlInjection: 0, xss: 0),
      (combined, stats) => AttackStats(
        ddos: combined.ddos + stats.ddos,
        sqlInjection: combined.sqlInjection + stats.sqlInjection,
        xss: combined.xss + stats.xss,
      ),
    );
  }
  
  /// Konversi ke Map untuk JSON
  Map<String, dynamic> toJson() {
    return {
      'ddos': ddos,
      'sqlInjection': sqlInjection,
      'xss': xss,
    };
  }
  
  /// Membuat dari Map JSON
  factory AttackStats.fromJson(Map<String, dynamic> json) {
    return AttackStats(
      ddos: json['ddos'] ?? 0,
      sqlInjection: json['sqlInjection'] ?? 0,
      xss: json['xss'] ?? 0,
    );
  }
  
  @override
  List<Object> get props => [ddos, sqlInjection, xss];
}

/// Log keamanan untuk domain
class SecurityLog extends Equatable {
  final DateTime timestamp;
  final String message;
  final bool isAlert;

  const SecurityLog({
    required this.timestamp,
    required this.message,
    required this.isAlert,
  });
  
  /// Membuat salinan dengan nilai yang diperbarui
  SecurityLog copyWith({
    DateTime? timestamp,
    String? message,
    bool? isAlert,
  }) {
    return SecurityLog(
      timestamp: timestamp ?? this.timestamp,
      message: message ?? this.message,
      isAlert: isAlert ?? this.isAlert,
    );
  }
  
  /// Konversi ke Map untuk JSON
  Map<String, dynamic> toJson() {
    return {
      'timestamp': timestamp.toIso8601String(),
      'message': message,
      'isAlert': isAlert,
    };
  }
  
  /// Membuat dari Map JSON
  factory SecurityLog.fromJson(Map<String, dynamic> json) {
    return SecurityLog(
      timestamp: DateTime.parse(json['timestamp']),
      message: json['message'],
      isAlert: json['isAlert'] ?? false,
    );
  }
  
  @override
  List<Object> get props => [timestamp, message, isAlert];
}

/// Serangan yang terdeteksi pada domain
class DetectedAttack extends Equatable {
  final AttackType type;
  final String status;
  final String severity;
  final DateTime detectedAt;
  final DateTime? resolvedAt;
  final String details;

  const DetectedAttack({
    required this.type,
    required this.status,
    required this.severity,
    required this.detectedAt,
    this.resolvedAt,
    required this.details,
  });
  
  /// Apakah serangan sudah diselesaikan
  bool get isResolved => resolvedAt != null;
  
  /// Durasi serangan (jika sudah diselesaikan)
  Duration? get duration {
    if (resolvedAt == null) return null;
    return resolvedAt!.difference(detectedAt);
  }
  
  /// Membuat salinan dengan nilai yang diperbarui
  DetectedAttack copyWith({
    AttackType? type,
    String? status,
    String? severity,
    DateTime? detectedAt,
    DateTime? resolvedAt,
    String? details,
  }) {
    return DetectedAttack(
      type: type ?? this.type,
      status: status ?? this.status,
      severity: severity ?? this.severity,
      detectedAt: detectedAt ?? this.detectedAt,
      resolvedAt: resolvedAt ?? this.resolvedAt,
      details: details ?? this.details,
    );
  }
  
  /// Konversi ke Map untuk JSON
  Map<String, dynamic> toJson() {
    return {
      'type': type.toString().split('.').last,
      'status': status,
      'severity': severity,
      'detectedAt': detectedAt.toIso8601String(),
      'resolvedAt': resolvedAt?.toIso8601String(),
      'details': details,
    };
  }
  
  /// Membuat dari Map JSON
  factory DetectedAttack.fromJson(Map<String, dynamic> json) {
    AttackType parseType(String type) {
      switch (type.toLowerCase()) {
        case 'ddos':
          return AttackType.ddos;
        case 'sqlinjection':
          return AttackType.sqlInjection;
        case 'xss':
          return AttackType.xss;
        default:
          return AttackType.sqlInjection;
      }
    }
    
    return DetectedAttack(
      type: parseType(json['type']),
      status: json['status'],
      severity: json['severity'],
      detectedAt: DateTime.parse(json['detectedAt']),
      resolvedAt: json['resolvedAt'] != null ? DateTime.parse(json['resolvedAt']) : null,
      details: json['details'],
    );
  }
  
  @override
  List<Object?> get props => [type, status, severity, detectedAt, resolvedAt, details];
}

/// Model domain
class Domain extends Equatable {
  final String id;
  final String url;
  final DateTime createdAt;
  final DateTime updatedAt;
  final AttackStats attackStats;
  final List<SecurityLog> securityLogs;
  final List<DetectedAttack> detectedAttacks;

  const Domain({
    required this.id,
    required this.url,
    required this.createdAt,
    required this.updatedAt,
    required this.attackStats,
    required this.securityLogs,
    required this.detectedAttacks,
  });
  
  /// Mendapatkan log keamanan terbaru
  List<SecurityLog> get recentLogs {
    final sortedLogs = List<SecurityLog>.from(securityLogs)
      ..sort((a, b) => b.timestamp.compareTo(a.timestamp));
    return sortedLogs.take(5).toList();
  }
  
  /// Mendapatkan serangan terbaru
  DetectedAttack? get latestAttack {
    if (detectedAttacks.isEmpty) return null;
    
    final sortedAttacks = List<DetectedAttack>.from(detectedAttacks)
      ..sort((a, b) => b.detectedAt.compareTo(a.detectedAt));
    return sortedAttacks.first;
  }
  
  /// Apakah domain memiliki alert
  bool get hasAlerts => securityLogs.any((log) => log.isAlert);
  
  /// Jumlah total serangan
  int get totalAttacks => attackStats.total;
  
  /// Membuat salinan dengan nilai yang diperbarui
  Domain copyWith({
    String? id,
    String? url,
    DateTime? createdAt,
    DateTime? updatedAt,
    AttackStats? attackStats,
    List<SecurityLog>? securityLogs,
    List<DetectedAttack>? detectedAttacks,
  }) {
    return Domain(
      id: id ?? this.id,
      url: url ?? this.url,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      attackStats: attackStats ?? this.attackStats,
      securityLogs: securityLogs ?? this.securityLogs,
      detectedAttacks: detectedAttacks ?? this.detectedAttacks,
    );
  }
  
  /// Konversi ke Map untuk JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'url': url,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'stats': attackStats.toJson(),
      'logs': securityLogs.map((log) => log.toJson()).toList(),
      'attacks': detectedAttacks.map((attack) => attack.toJson()).toList(),
    };
  }
  
  /// Membuat dari Map JSON
  factory Domain.fromJson(Map<String, dynamic> json) {
    return Domain(
      id: json['id'],
      url: json['url'],
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
      attackStats: AttackStats.fromJson(json['stats']),
      securityLogs: (json['logs'] as List<dynamic>?)
          ?.map((log) => SecurityLog.fromJson(log))
          .toList() ?? [],
      detectedAttacks: (json['attacks'] as List<dynamic>?)
          ?.map((attack) => DetectedAttack.fromJson(attack))
          .toList() ?? [],
    );
  }
  
  @override
  List<Object> get props => [id, url];
}