import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:syra_security/core/constants.dart';
import 'package:syra_security/core/utils.dart';
import 'package:syra_security/core/widgets/common_widgets.dart';
import 'package:syra_security/features/auth/presentation/screens/login_screen.dart';
import 'package:syra_security/features/dashboard/presentation/screens/dashboard_screen.dart';
import 'package:syra_security/features/domains/presentation/screens/add_domain_screen.dart';
import 'package:syra_security/features/domains/presentation/screens/domain_detail_screen.dart';
import 'package:syra_security/features/domains/presentation/providers/domain_provider.dart';

/// Enum untuk nama route
enum AppRoute {
  login,
  dashboard,
  domainDetail,
  addDomain,
}

/// Extension untuk AppRoute
extension AppRouteExtension on AppRoute {
  String get path {
    switch (this) {
      case AppRoute.login:
        return AppConstants.loginRoute;
      case AppRoute.dashboard:
        return AppConstants.dashboardRoute;
      case AppRoute.domainDetail:
        return '${AppConstants.domainDetailRoute}/:id';
      case AppRoute.addDomain:
        return AppConstants.addDomainRoute;
    }
  }
  
  String get name {
    return toString().split('.').last;
  }
}

/// Membuat router aplikasi
GoRouter createRouter(WidgetRef ref) {
  AppUtils.logInfo('Creating application router');

  return GoRouter(
    initialLocation: AppConstants.loginRoute,
    debugLogDiagnostics: true,
    redirect: (context, state) {
      AppUtils.logInfo('Router redirect: ${state.uri.path}');

      // Cek status autentikasi
      final authState = ref.read(authProvider);
      final isAuthenticated = authState.isAuthenticated;
      final isLoginRoute = state.uri.path == AppConstants.loginRoute;

      // Jika belum login dan bukan di halaman login, redirect ke login
      if (!isAuthenticated && !isLoginRoute) {
        AppUtils.logInfo('User not authenticated, redirecting to login');
        return AppConstants.loginRoute;
      }

      // Jika sudah login dan di halaman login, redirect ke dashboard
      if (isAuthenticated && isLoginRoute) {
        AppUtils.logInfo('User already authenticated, redirecting to dashboard');
        return AppConstants.dashboardRoute;
      }

      return null;
    },
    routes: [
      // Login Route
      GoRoute(
        path: AppRoute.login.path,
        name: AppRoute.login.name,
        builder: (context, state) {
          AppUtils.logInfo('Navigating to LoginScreen');
          return const LoginScreen();
        },
      ),
      
      // Dashboard Route
      GoRoute(
        path: AppRoute.dashboard.path,
        name: AppRoute.dashboard.name,
        builder: (context, state) {
          AppUtils.logInfo('Navigating to DashboardScreen');
          return const DashboardScreen();
        },
      ),
      
      // Domain Detail Route
      GoRoute(
        path: AppRoute.domainDetail.path,
        name: AppRoute.domainDetail.name,
        builder: (context, state) {
          final domainId = state.pathParameters['id'] ?? '';
          AppUtils.logInfo('Navigating to DomainDetailScreen with id: $domainId');
          
          if (domainId.isEmpty) {
            AppUtils.logError('Domain ID is empty');
            return ErrorDisplay(
              message: 'ID Domain tidak valid',
              onRetry: () => GoRouter.of(context).go(AppConstants.dashboardRoute),
            );
          }
          
          return DomainDetailScreen(domainId: domainId);
        },
      ),
      
      // Add Domain Route
      GoRoute(
        path: AppRoute.addDomain.path,
        name: AppRoute.addDomain.name,
        builder: (context, state) {
          AppUtils.logInfo('Navigating to AddDomainScreen');
          return const AddDomainScreen();
        },
      ),
    ],
    errorBuilder: (context, state) {
      AppUtils.logError('Router error', state.error);
      return Scaffold(
        appBar: AppBar(
          title: const Text('Halaman Tidak Ditemukan'),
        ),
        body: ErrorDisplay(
          message: 'Halaman yang Anda cari tidak ditemukan',
          icon: Icons.not_listed_location,
          onRetry: () => GoRouter.of(context).go(AppConstants.dashboardRoute),
        ),
      );
    },
  );
}

/// Extension untuk GoRouter
extension GoRouterExtension on GoRouter {
  /// Navigasi ke halaman login
  void goToLogin() => go(AppConstants.loginRoute);
  
  /// Navigasi ke halaman dashboard
  void goToDashboard() => go(AppConstants.dashboardRoute);
  
  /// Navigasi ke halaman detail domain
  void goToDomainDetail(String domainId) => go('${AppConstants.domainDetailRoute}/$domainId');
  
  /// Navigasi ke halaman tambah domain
  void goToAddDomain() => go(AppConstants.addDomainRoute);
}

/// Extension untuk BuildContext
extension RouterContextExtension on BuildContext {
  /// Mendapatkan router
  GoRouter get router => GoRouter.of(this);
  
  /// Navigasi ke halaman login
  void goToLogin() => router.goToLogin();
  
  /// Navigasi ke halaman dashboard
  void goToDashboard() => router.goToDashboard();
  
  /// Navigasi ke halaman detail domain
  void goToDomainDetail(String domainId) => router.goToDomainDetail(domainId);
  
  /// Navigasi ke halaman tambah domain
  void goToAddDomain() => router.goToAddDomain();
}