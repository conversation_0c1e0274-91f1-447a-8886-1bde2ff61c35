import 'dart:async';
import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:syra_security/core/exceptions.dart';
import 'package:syra_security/core/utils.dart';

/// Enum untuk tipe vulnerability
enum VulnerabilityType {
  sqlInjection,
  xss,
  csrf,
  openRedirect,
  directoryTraversal,
  commandInjection,
  fileUpload,
  weakAuthentication,
  insecureHeaders,
  mixedContent,
}

/// Model untuk vulnerability yang ditemukan
class Vulnerability {
  final VulnerabilityType type;
  final String severity; // Low, Medium, High, Critical
  final String description;
  final String location;
  final String? recommendation;
  final DateTime detectedAt;

  const Vulnerability({
    required this.type,
    required this.severity,
    required this.description,
    required this.location,
    this.recommendation,
    required this.detectedAt,
  });

  factory Vulnerability.fromJson(Map<String, dynamic> json) {
    return Vulnerability(
      type: VulnerabilityType.values.firstWhere(
        (e) => e.toString().split('.').last == json['type'],
        orElse: () => VulnerabilityType.xss,
      ),
      severity: json['severity'] ?? 'Medium',
      description: json['description'] ?? '',
      location: json['location'] ?? '',
      recommendation: json['recommendation'],
      detectedAt: DateTime.parse(json['detectedAt'] ?? DateTime.now().toIso8601String()),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'type': type.toString().split('.').last,
      'severity': severity,
      'description': description,
      'location': location,
      'recommendation': recommendation,
      'detectedAt': detectedAt.toIso8601String(),
    };
  }
}

/// Model untuk hasil scan
class ScanResult {
  final String domainId;
  final String domainUrl;
  final DateTime scanStarted;
  final DateTime? scanCompleted;
  final bool isCompleted;
  final List<Vulnerability> vulnerabilities;
  final Map<String, dynamic> metadata;

  const ScanResult({
    required this.domainId,
    required this.domainUrl,
    required this.scanStarted,
    this.scanCompleted,
    this.isCompleted = false,
    this.vulnerabilities = const [],
    this.metadata = const {},
  });

  factory ScanResult.fromJson(Map<String, dynamic> json) {
    return ScanResult(
      domainId: json['domainId'] ?? '',
      domainUrl: json['domainUrl'] ?? '',
      scanStarted: DateTime.parse(json['scanStarted'] ?? DateTime.now().toIso8601String()),
      scanCompleted: json['scanCompleted'] != null 
          ? DateTime.parse(json['scanCompleted'])
          : null,
      isCompleted: json['isCompleted'] ?? false,
      vulnerabilities: (json['vulnerabilities'] as List<dynamic>?)
          ?.map((v) => Vulnerability.fromJson(v))
          .toList() ?? [],
      metadata: json['metadata'] ?? {},
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'domainId': domainId,
      'domainUrl': domainUrl,
      'scanStarted': scanStarted.toIso8601String(),
      'scanCompleted': scanCompleted?.toIso8601String(),
      'isCompleted': isCompleted,
      'vulnerabilities': vulnerabilities.map((v) => v.toJson()).toList(),
      'metadata': metadata,
    };
  }

  /// Get vulnerabilities by severity
  List<Vulnerability> getVulnerabilitiesBySeverity(String severity) {
    return vulnerabilities.where((v) => v.severity == severity).toList();
  }

  /// Get vulnerability count by type
  Map<VulnerabilityType, int> getVulnerabilityCountByType() {
    final Map<VulnerabilityType, int> counts = {};
    for (final vuln in vulnerabilities) {
      counts[vuln.type] = (counts[vuln.type] ?? 0) + 1;
    }
    return counts;
  }
}

/// Service untuk security scanning
class SecurityScannerService {
  final String baseUrl;
  final http.Client _client;
  final Duration scanTimeout;

  SecurityScannerService({
    required this.baseUrl,
    http.Client? client,
    this.scanTimeout = const Duration(minutes: 5),
  }) : _client = client ?? http.Client();

  /// Mulai security scan untuk domain
  Future<ScanResult> startScan(String domainId, String domainUrl) async {
    AppUtils.logInfo('Starting security scan for domain: $domainUrl');

    try {
      final response = await _client.post(
        Uri.parse('$baseUrl/api/security/scan'),
        headers: {
          'Content-Type': 'application/json',
        },
        body: json.encode({
          'domainId': domainId,
          'domainUrl': domainUrl,
        }),
      ).timeout(const Duration(seconds: 30));

      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        return ScanResult.fromJson(responseData);
      } else {
        throw ServerException(
          'Failed to start security scan',
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      AppUtils.logWarning('Security scan API failed, using mock scan: $e');
      return _performMockScan(domainId, domainUrl);
    }
  }

  /// Get status scan yang sedang berjalan
  Future<ScanResult> getScanStatus(String domainId) async {
    AppUtils.logInfo('Getting scan status for domain: $domainId');

    try {
      final response = await _client.get(
        Uri.parse('$baseUrl/api/security/scan/$domainId'),
        headers: {
          'Content-Type': 'application/json',
        },
      ).timeout(const Duration(seconds: 10));

      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        return ScanResult.fromJson(responseData);
      } else {
        throw ServerException(
          'Failed to get scan status',
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      AppUtils.logWarning('Get scan status API failed: $e');
      throw NetworkException('Failed to get scan status: $e');
    }
  }

  /// Perform basic domain health check
  Future<Map<String, dynamic>> performHealthCheck(String domainUrl) async {
    AppUtils.logInfo('Performing health check for: $domainUrl');

    try {
      final cleanUrl = AppUtils.cleanDomainUrl(domainUrl);
      final uri = Uri.parse('https://$cleanUrl');
      
      final stopwatch = Stopwatch()..start();
      final response = await _client.get(uri).timeout(const Duration(seconds: 10));
      stopwatch.stop();

      final healthData = {
        'url': cleanUrl,
        'status': response.statusCode,
        'responseTime': stopwatch.elapsedMilliseconds,
        'isHealthy': response.statusCode >= 200 && response.statusCode < 400,
        'headers': response.headers,
        'timestamp': DateTime.now().toIso8601String(),
      };

      // Analyze security headers
      final securityHeaders = _analyzeSecurityHeaders(response.headers);
      healthData['securityHeaders'] = securityHeaders;

      AppUtils.logInfo('Health check completed for $cleanUrl: ${healthData['status']}');
      return healthData;

    } catch (e) {
      AppUtils.logError('Health check failed for $domainUrl', e);
      return {
        'url': domainUrl,
        'status': 0,
        'responseTime': 0,
        'isHealthy': false,
        'error': e.toString(),
        'timestamp': DateTime.now().toIso8601String(),
      };
    }
  }

  /// Analyze security headers
  Map<String, dynamic> _analyzeSecurityHeaders(Map<String, String> headers) {
    final securityHeaders = <String, dynamic>{};
    
    // Check for important security headers
    final importantHeaders = [
      'strict-transport-security',
      'content-security-policy',
      'x-frame-options',
      'x-content-type-options',
      'x-xss-protection',
      'referrer-policy',
    ];

    for (final header in importantHeaders) {
      securityHeaders[header] = headers.containsKey(header);
    }

    // Calculate security score
    final presentHeaders = securityHeaders.values.where((v) => v == true).length;
    securityHeaders['score'] = (presentHeaders / importantHeaders.length * 100).round();

    return securityHeaders;
  }

  /// Perform mock security scan untuk development
  Future<ScanResult> _performMockScan(String domainId, String domainUrl) async {
    AppUtils.logInfo('Performing mock security scan for: $domainUrl');
    
    // Simulate scan delay
    await Future.delayed(const Duration(seconds: 2));

    // Generate mock vulnerabilities
    final vulnerabilities = <Vulnerability>[];
    
    // Add some random vulnerabilities for demo
    if (domainUrl.contains('test') || domainUrl.contains('demo')) {
      vulnerabilities.addAll([
        Vulnerability(
          type: VulnerabilityType.xss,
          severity: 'Medium',
          description: 'Potential XSS vulnerability detected in search parameter',
          location: '/search?q=<script>',
          recommendation: 'Implement proper input validation and output encoding',
          detectedAt: DateTime.now(),
        ),
        Vulnerability(
          type: VulnerabilityType.insecureHeaders,
          severity: 'Low',
          description: 'Missing security headers',
          location: 'HTTP Response Headers',
          recommendation: 'Add Content-Security-Policy and X-Frame-Options headers',
          detectedAt: DateTime.now(),
        ),
      ]);
    }

    return ScanResult(
      domainId: domainId,
      domainUrl: domainUrl,
      scanStarted: DateTime.now().subtract(const Duration(seconds: 2)),
      scanCompleted: DateTime.now(),
      isCompleted: true,
      vulnerabilities: vulnerabilities,
      metadata: {
        'scanType': 'mock',
        'totalChecks': 15,
        'completedChecks': 15,
      },
    );
  }

  /// Generate security report
  Future<Map<String, dynamic>> generateSecurityReport(String domainId) async {
    AppUtils.logInfo('Generating security report for domain: $domainId');

    try {
      final scanResult = await getScanStatus(domainId);
      
      final report = {
        'domainId': domainId,
        'domainUrl': scanResult.domainUrl,
        'generatedAt': DateTime.now().toIso8601String(),
        'scanResult': scanResult.toJson(),
        'summary': _generateSummary(scanResult),
        'recommendations': _generateRecommendations(scanResult),
      };

      return report;
    } catch (e) {
      AppUtils.logError('Failed to generate security report', e);
      throw ServerException('Failed to generate security report: $e');
    }
  }

  /// Generate summary dari scan result
  Map<String, dynamic> _generateSummary(ScanResult scanResult) {
    final summary = <String, dynamic>{};
    
    summary['totalVulnerabilities'] = scanResult.vulnerabilities.length;
    summary['criticalCount'] = scanResult.getVulnerabilitiesBySeverity('Critical').length;
    summary['highCount'] = scanResult.getVulnerabilitiesBySeverity('High').length;
    summary['mediumCount'] = scanResult.getVulnerabilitiesBySeverity('Medium').length;
    summary['lowCount'] = scanResult.getVulnerabilitiesBySeverity('Low').length;
    
    // Calculate risk score
    final criticalWeight = summary['criticalCount'] * 10;
    final highWeight = summary['highCount'] * 7;
    final mediumWeight = summary['mediumCount'] * 4;
    final lowWeight = summary['lowCount'] * 1;
    
    summary['riskScore'] = criticalWeight + highWeight + mediumWeight + lowWeight;
    
    // Determine risk level
    if (summary['riskScore'] >= 50) {
      summary['riskLevel'] = 'Critical';
    } else if (summary['riskScore'] >= 25) {
      summary['riskLevel'] = 'High';
    } else if (summary['riskScore'] >= 10) {
      summary['riskLevel'] = 'Medium';
    } else {
      summary['riskLevel'] = 'Low';
    }

    return summary;
  }

  /// Generate recommendations
  List<String> _generateRecommendations(ScanResult scanResult) {
    final recommendations = <String>[];
    
    if (scanResult.vulnerabilities.isEmpty) {
      recommendations.add('Domain Anda terlihat aman. Lanjutkan monitoring secara berkala.');
      return recommendations;
    }

    final vulnCounts = scanResult.getVulnerabilityCountByType();
    
    if (vulnCounts.containsKey(VulnerabilityType.xss)) {
      recommendations.add('Implementasikan validasi input dan output encoding untuk mencegah XSS.');
    }
    
    if (vulnCounts.containsKey(VulnerabilityType.sqlInjection)) {
      recommendations.add('Gunakan prepared statements untuk mencegah SQL injection.');
    }
    
    if (vulnCounts.containsKey(VulnerabilityType.insecureHeaders)) {
      recommendations.add('Tambahkan security headers seperti CSP, HSTS, dan X-Frame-Options.');
    }

    recommendations.add('Lakukan security scan secara berkala untuk memantau keamanan domain.');
    
    return recommendations;
  }
}
