import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

/// Extension untuk DateTime
extension DateTimeExtension on DateTime {
  /// Format tanggal ke format yang lebih mudah dibaca
  String toFormattedDate() {
    return DateFormat('d MMM yyyy').format(this);
  }
  
  /// Format tanggal dan waktu ke format yang lebih mudah dibaca
  String toFormattedDateTime() {
    return DateFormat('d MMM yyyy, HH:mm').format(this);
  }
  
  /// Format waktu ke format yang lebih mudah dibaca
  String toFormattedTime() {
    return DateFormat('HH:mm').format(this);
  }
  
  /// Apakah tanggal ini hari ini
  bool get isToday {
    final now = DateTime.now();
    return year == now.year && month == now.month && day == now.day;
  }
  
  /// Apakah tanggal ini kemarin
  bool get isYesterday {
    final yesterday = DateTime.now().subtract(const Duration(days: 1));
    return year == yesterday.year && month == yesterday.month && day == yesterday.day;
  }
  
  /// Format relatif (hari ini, kemarin, atau tanggal)
  String toRelativeDate() {
    if (isToday) {
      return 'Hari ini, ${toFormattedTime()}';
    } else if (isYesterday) {
      return 'Kemarin, ${toFormattedTime()}';
    } else {
      return toFormattedDateTime();
    }
  }
}

/// Extension untuk String
extension StringExtension on String {
  /// Kapitalisasi huruf pertama setiap kata
  String toTitleCase() {
    if (isEmpty) return this;
    return split(' ').map((word) {
      if (word.isEmpty) return word;
      return word[0].toUpperCase() + word.substring(1).toLowerCase();
    }).join(' ');
  }
  
  /// Kapitalisasi huruf pertama
  String toCapitalized() {
    if (isEmpty) return this;
    return this[0].toUpperCase() + substring(1);
  }
  
  /// Potong string jika terlalu panjang
  String truncate(int maxLength, {String suffix = '...'}) {
    if (length <= maxLength) return this;
    return '${substring(0, maxLength)}$suffix';
  }
  
  /// Validasi email
  bool get isValidEmail {
    final emailRegex = RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$');
    return emailRegex.hasMatch(this);
  }
  
  /// Validasi URL
  bool get isValidUrl {
    final urlRegex = RegExp(r'^(http|https)://[a-zA-Z0-9]([a-zA-Z0-9-]*[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9-]*[a-zA-Z0-9])?)+(/.*)?$');
    return urlRegex.hasMatch(this);
  }
  
  /// Validasi domain
  bool get isValidDomain {
    final domainRegex = RegExp(r'^[a-zA-Z0-9][a-zA-Z0-9.-]*[a-zA-Z0-9]\.[a-zA-Z]{2,}$');
    return domainRegex.hasMatch(this);
  }
}

/// Extension untuk Duration
extension DurationExtension on Duration {
  /// Format durasi ke format yang lebih mudah dibaca
  String toFormattedString() {
    final seconds = inSeconds;
    final minutes = inMinutes;
    final hours = inHours;
    final days = inDays;
    
    if (days > 0) {
      return '$days hari ${hours % 24} jam';
    } else if (hours > 0) {
      return '$hours jam ${minutes % 60} menit';
    } else if (minutes > 0) {
      return '$minutes menit ${seconds % 60} detik';
    } else {
      return '$seconds detik';
    }
  }
}

/// Extension untuk BuildContext
extension BuildContextExtension on BuildContext {
  /// Mendapatkan ukuran layar
  Size get screenSize => MediaQuery.of(this).size;
  
  /// Mendapatkan lebar layar
  double get screenWidth => MediaQuery.of(this).size.width;
  
  /// Mendapatkan tinggi layar
  double get screenHeight => MediaQuery.of(this).size.height;
  
  /// Mendapatkan tema
  ThemeData get theme => Theme.of(this);
  
  /// Mendapatkan warna primer
  Color get primaryColor => theme.primaryColor;
  
  /// Mendapatkan warna aksen
  Color get accentColor => theme.colorScheme.secondary;
  
  /// Mendapatkan text theme
  TextTheme get textTheme => theme.textTheme;
  
  /// Menutup keyboard
  void hideKeyboard() {
    FocusScope.of(this).unfocus();
  }
  
  /// Menampilkan snackbar
  void showSnackBar(String message, {
    Color backgroundColor = Colors.green,
    Duration duration = const Duration(seconds: 3),
    SnackBarAction? action,
  }) {
    ScaffoldMessenger.of(this).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: backgroundColor,
        duration: duration,
        action: action,
      ),
    );
  }
}

/// Extension untuk List
extension ListExtension<T> on List<T> {
  /// Mendapatkan elemen terakhir atau null jika list kosong
  T? get lastOrNull => isEmpty ? null : last;
  
  /// Mendapatkan elemen pertama atau null jika list kosong
  T? get firstOrNull => isEmpty ? null : first;
  
  /// Mendapatkan elemen pada index atau null jika index di luar batas
  T? elementAtOrNull(int index) {
    if (index < 0 || index >= length) return null;
    return this[index];
  }
  
  /// Mengelompokkan list berdasarkan key
  Map<K, List<T>> groupBy<K>(K Function(T) keyFunction) {
    final result = <K, List<T>>{};
    for (final element in this) {
      final key = keyFunction(element);
      if (!result.containsKey(key)) {
        result[key] = [];
      }
      result[key]!.add(element);
    }
    return result;
  }
}