import 'package:flutter/foundation.dart';
import 'package:intl/intl.dart';

/// Kelas utilitas untuk fungsi-fungsi umum yang digunakan di seluruh aplikasi
class AppUtils {
  /// Format tanggal ke format yang lebih mudah dibaca
  static String formatDate(DateTime date) {
    return DateFormat('d MMM yyyy').format(date);
  }
  
  /// Format tanggal dan waktu ke format yang lebih mudah dibaca
  static String formatDateTime(DateTime dateTime) {
    return DateFormat('d MMM yyyy, HH:mm').format(dateTime);
  }
  
  /// Bersihkan URL domain dari protokol dan trailing slash
  static String cleanDomainUrl(String url) {
    String cleanUrl = url.trim();
    cleanUrl = cleanUrl.replaceAll(RegExp(r'^https?://'), '');
    cleanUrl = cleanUrl.replaceAll(RegExp(r'/$'), '');
    return cleanUrl;
  }
  
  /// Validasi format domain
  static bool isValidDomain(String domain) {
    final domainRegex = RegExp(r'^[a-zA-Z0-9][a-zA-Z0-9.-]*[a-zA-Z0-9]\.[a-zA-Z]{2,}$');
    return domainRegex.hasMatch(domain);
  }
  
  /// Log pesan dengan level yang berbeda
  static void logInfo(String message) {
    if (kDebugMode) {
      debugPrint('INFO: $message');
    }
  }
  
  static void logWarning(String message) {
    if (kDebugMode) {
      debugPrint('WARNING: $message');
    }
  }
  
  static void logError(String message, [dynamic error, StackTrace? stackTrace]) {
    if (kDebugMode) {
      debugPrint('ERROR: $message');
      if (error != null) {
        debugPrint('Error details: $error');
      }
      if (stackTrace != null) {
        debugPrint('Stack trace: $stackTrace');
      }
    }
  }
}