import 'package:flutter/material.dart';

class GoogleSignInButton extends StatelessWidget {
  final VoidCallback onPressed;

  const GoogleSignInButton({
    super.key,
    required this.onPressed,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      height: 56,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade300, width: 1),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onPressed,
          borderRadius: BorderRadius.circular(8),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _buildGoogleLogo(),
              const SizedBox(width: 12),
              const Text(
                'Sign in with Google',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: Color(0xFF1F1F1F),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildGoogleLogo() {
    return SizedBox(
      width: 24,
      height: 24,
      child: CustomPaint(
        painter: GoogleLogoPainter(),
      ),
    );
  }
}

class GoogleLogoPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final double width = size.width;
    final double height = size.height;
    final double centerX = width / 2;
    final double centerY = height / 2;

    // Colors for Google logo (official colors)
    final Paint bluePaint = Paint()..color = const Color(0xFF4285F4);
    final Paint redPaint = Paint()..color = const Color(0xFFEA4335);
    final Paint yellowPaint = Paint()..color = const Color(0xFFFBBC05);
    final Paint greenPaint = Paint()..color = const Color(0xFF34A853);

    // Draw the Google "G" logo
    final double outerRadius = width * 0.45;
    final double innerRadius = width * 0.25;

    // Blue section (top-right)
    final bluePath = Path();
    bluePath.moveTo(centerX, centerY - outerRadius);
    bluePath.arcTo(
      Rect.fromCircle(center: Offset(centerX, centerY), radius: outerRadius),
      -90 * (3.14159 / 180), // -90 degrees
      90 * (3.14159 / 180),  // 90 degrees sweep
      false,
    );
    bluePath.lineTo(centerX + innerRadius, centerY);
    bluePath.arcTo(
      Rect.fromCircle(center: Offset(centerX, centerY), radius: innerRadius),
      0, // 0 degrees
      -90 * (3.14159 / 180), // -90 degrees sweep
      false,
    );
    bluePath.close();
    canvas.drawPath(bluePath, bluePaint);

    // Red section (bottom-right)
    final redPath = Path();
    redPath.moveTo(centerX + outerRadius, centerY);
    redPath.arcTo(
      Rect.fromCircle(center: Offset(centerX, centerY), radius: outerRadius),
      0, // 0 degrees
      90 * (3.14159 / 180), // 90 degrees sweep
      false,
    );
    redPath.lineTo(centerX, centerY + innerRadius);
    redPath.arcTo(
      Rect.fromCircle(center: Offset(centerX, centerY), radius: innerRadius),
      90 * (3.14159 / 180), // 90 degrees
      -90 * (3.14159 / 180), // -90 degrees sweep
      false,
    );
    redPath.close();
    canvas.drawPath(redPath, redPaint);

    // Yellow section (bottom-left)
    final yellowPath = Path();
    yellowPath.moveTo(centerX, centerY + outerRadius);
    yellowPath.arcTo(
      Rect.fromCircle(center: Offset(centerX, centerY), radius: outerRadius),
      90 * (3.14159 / 180), // 90 degrees
      90 * (3.14159 / 180), // 90 degrees sweep
      false,
    );
    yellowPath.lineTo(centerX - innerRadius, centerY);
    yellowPath.arcTo(
      Rect.fromCircle(center: Offset(centerX, centerY), radius: innerRadius),
      180 * (3.14159 / 180), // 180 degrees
      -90 * (3.14159 / 180), // -90 degrees sweep
      false,
    );
    yellowPath.close();
    canvas.drawPath(yellowPath, yellowPaint);

    // Green section (top-left)
    final greenPath = Path();
    greenPath.moveTo(centerX - outerRadius, centerY);
    greenPath.arcTo(
      Rect.fromCircle(center: Offset(centerX, centerY), radius: outerRadius),
      180 * (3.14159 / 180), // 180 degrees
      90 * (3.14159 / 180),  // 90 degrees sweep
      false,
    );
    greenPath.lineTo(centerX, centerY - innerRadius);
    greenPath.arcTo(
      Rect.fromCircle(center: Offset(centerX, centerY), radius: innerRadius),
      270 * (3.14159 / 180), // 270 degrees
      -90 * (3.14159 / 180), // -90 degrees sweep
      false,
    );
    greenPath.close();
    canvas.drawPath(greenPath, greenPaint);

    // Add the horizontal bar on the right (part of the "G")
    final barPaint = Paint()..color = const Color(0xFF4285F4);
    final barRect = RRect.fromRectAndRadius(
      Rect.fromLTWH(
        centerX,
        centerY - width * 0.06,
        width * 0.25,
        width * 0.12,
      ),
      Radius.circular(width * 0.06),
    );
    canvas.drawRRect(barRect, barPaint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}