# Flutter/Dart
.dart_tool/
.packages
.pub-cache/
.pub/
build/
.flutter-plugins
.flutter-plugins-dependencies
.fvm/

# Android
*.jks
*.p8
*.p12
*.key
*.mobileprovision
android/app/google-services.json
android/key.properties

# iOS
ios/Runner/GoogleService-Info.plist
ios/Flutter/flutter_export_environment.sh

# Web
web/

# Coverage
coverage/

# Exceptions
!**/ios/**/default.mode1v3
!**/ios/**/default.mode2v3
!**/ios/**/default.pbxuser
!**/ios/**/default.perspectivev3

# macOS
.DS_Store
*.pem

# Local env files
.env*.local
.env

# IDE
.vscode/
.idea/
*.swp
*.swo

# Logs
*.log

# Dependencies
node_modules/
