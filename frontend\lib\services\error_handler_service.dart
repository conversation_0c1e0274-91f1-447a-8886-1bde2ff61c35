import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:syra_security/core/exceptions.dart';
import 'package:syra_security/core/utils.dart';

/// Enum untuk tipe error
enum ErrorType {
  network,
  authentication,
  validation,
  server,
  timeout,
  cache,
  permission,
  unknown,
}

/// Model untuk error info
class ErrorInfo {
  final ErrorType type;
  final String message;
  final String userMessage;
  final String? code;
  final dynamic details;
  final DateTime timestamp;
  final bool isRetryable;

  const ErrorInfo({
    required this.type,
    required this.message,
    required this.userMessage,
    this.code,
    this.details,
    required this.timestamp,
    this.isRetryable = false,
  });

  factory ErrorInfo.fromException(dynamic exception) {
    final timestamp = DateTime.now();
    
    if (exception is AuthenticationException) {
      return ErrorInfo(
        type: ErrorType.authentication,
        message: exception.message,
        userMessage: '<PERSON>si Anda telah berak<PERSON>. <PERSON><PERSON>an login kembali.',
        code: exception.code,
        details: exception.details,
        timestamp: timestamp,
        isRetryable: false,
      );
    }
    
    if (exception is NetworkException) {
      return ErrorInfo(
        type: ErrorType.network,
        message: exception.message,
        userMessage: 'Tidak dapat terhubung ke server. Periksa koneksi internet Anda.',
        code: exception.code,
        details: exception.details,
        timestamp: timestamp,
        isRetryable: true,
      );
    }
    
    if (exception is TimeoutException) {
      return ErrorInfo(
        type: ErrorType.timeout,
        message: exception.message,
        userMessage: 'Koneksi timeout. Silakan coba lagi.',
        code: exception.code,
        details: exception.details,
        timestamp: timestamp,
        isRetryable: true,
      );
    }
    
    if (exception is ValidationException) {
      return ErrorInfo(
        type: ErrorType.validation,
        message: exception.message,
        userMessage: exception.message, // Validation messages are usually user-friendly
        code: exception.code,
        details: exception.details,
        timestamp: timestamp,
        isRetryable: false,
      );
    }
    
    if (exception is ServerException) {
      String userMessage = 'Terjadi kesalahan pada server.';
      bool isRetryable = true;
      
      if (exception.statusCode != null) {
        switch (exception.statusCode!) {
          case 400:
            userMessage = 'Permintaan tidak valid.';
            isRetryable = false;
            break;
          case 401:
            userMessage = 'Sesi Anda telah berakhir. Silakan login kembali.';
            isRetryable = false;
            break;
          case 403:
            userMessage = 'Anda tidak memiliki izin untuk melakukan aksi ini.';
            isRetryable = false;
            break;
          case 404:
            userMessage = 'Data yang diminta tidak ditemukan.';
            isRetryable = false;
            break;
          case 429:
            userMessage = 'Terlalu banyak permintaan. Silakan coba lagi nanti.';
            isRetryable = true;
            break;
          case 500:
          case 502:
          case 503:
          case 504:
            userMessage = 'Server sedang mengalami gangguan. Silakan coba lagi nanti.';
            isRetryable = true;
            break;
        }
      }
      
      return ErrorInfo(
        type: ErrorType.server,
        message: exception.message,
        userMessage: userMessage,
        code: exception.code,
        details: exception.details,
        timestamp: timestamp,
        isRetryable: isRetryable,
      );
    }
    
    if (exception is SocketException) {
      return ErrorInfo(
        type: ErrorType.network,
        message: exception.message,
        userMessage: 'Tidak dapat terhubung ke server. Periksa koneksi internet Anda.',
        timestamp: timestamp,
        isRetryable: true,
      );
    }
    
    if (exception is TimeoutException) {
      return ErrorInfo(
        type: ErrorType.timeout,
        message: exception.toString(),
        userMessage: 'Koneksi timeout. Silakan coba lagi.',
        timestamp: timestamp,
        isRetryable: true,
      );
    }
    
    // Default untuk exception yang tidak dikenal
    return ErrorInfo(
      type: ErrorType.unknown,
      message: exception.toString(),
      userMessage: 'Terjadi kesalahan yang tidak terduga. Silakan coba lagi.',
      timestamp: timestamp,
      isRetryable: true,
    );
  }
}

/// Service untuk menangani error secara global
class ErrorHandlerService {
  static final ErrorHandlerService _instance = ErrorHandlerService._internal();
  factory ErrorHandlerService() => _instance;
  ErrorHandlerService._internal();

  final StreamController<ErrorInfo> _errorController = StreamController<ErrorInfo>.broadcast();
  
  /// Stream untuk mendengarkan error
  Stream<ErrorInfo> get errorStream => _errorController.stream;

  /// Handle error dan convert ke ErrorInfo
  ErrorInfo handleError(dynamic error, {StackTrace? stackTrace}) {
    final errorInfo = ErrorInfo.fromException(error);
    
    // Log error
    AppUtils.logError(
      'Error handled: ${errorInfo.type.name}',
      error,
      stackTrace,
    );
    
    // Emit error ke stream
    _errorController.add(errorInfo);
    
    return errorInfo;
  }

  /// Show error dialog
  void showErrorDialog(BuildContext context, ErrorInfo errorInfo, {VoidCallback? onRetry}) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(
              _getErrorIcon(errorInfo.type),
              color: _getErrorColor(errorInfo.type),
            ),
            const SizedBox(width: 8),
            const Text('Error'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(errorInfo.userMessage),
            if (errorInfo.code != null) ...[
              const SizedBox(height: 8),
              Text(
                'Error Code: ${errorInfo.code}',
                style: Theme.of(context).textTheme.bodySmall,
              ),
            ],
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
          if (errorInfo.isRetryable && onRetry != null)
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                onRetry();
              },
              child: const Text('Coba Lagi'),
            ),
        ],
      ),
    );
  }

  /// Show error snackbar
  void showErrorSnackBar(BuildContext context, ErrorInfo errorInfo, {VoidCallback? onRetry}) {
    final messenger = ScaffoldMessenger.of(context);
    
    messenger.showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(
              _getErrorIcon(errorInfo.type),
              color: Colors.white,
              size: 20,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                errorInfo.userMessage,
                style: const TextStyle(color: Colors.white),
              ),
            ),
          ],
        ),
        backgroundColor: _getErrorColor(errorInfo.type),
        duration: const Duration(seconds: 4),
        action: errorInfo.isRetryable && onRetry != null
            ? SnackBarAction(
                label: 'Coba Lagi',
                textColor: Colors.white,
                onPressed: onRetry,
              )
            : null,
      ),
    );
  }

  /// Get icon untuk error type
  IconData _getErrorIcon(ErrorType type) {
    switch (type) {
      case ErrorType.network:
      case ErrorType.timeout:
        return Icons.wifi_off;
      case ErrorType.authentication:
        return Icons.lock_outline;
      case ErrorType.validation:
        return Icons.warning_outlined;
      case ErrorType.server:
        return Icons.error_outline;
      case ErrorType.permission:
        return Icons.block;
      default:
        return Icons.error_outline;
    }
  }

  /// Get color untuk error type
  Color _getErrorColor(ErrorType type) {
    switch (type) {
      case ErrorType.network:
      case ErrorType.timeout:
        return Colors.orange;
      case ErrorType.authentication:
        return Colors.red;
      case ErrorType.validation:
        return Colors.amber;
      case ErrorType.server:
        return Colors.red;
      case ErrorType.permission:
        return Colors.purple;
      default:
        return Colors.red;
    }
  }

  /// Dispose resources
  void dispose() {
    _errorController.close();
  }
}

/// Extension untuk BuildContext
extension ErrorHandlerExtension on BuildContext {
  /// Show error dengan context
  void showError(dynamic error, {VoidCallback? onRetry, bool useDialog = false}) {
    final errorHandler = ErrorHandlerService();
    final errorInfo = errorHandler.handleError(error);
    
    if (useDialog) {
      errorHandler.showErrorDialog(this, errorInfo, onRetry: onRetry);
    } else {
      errorHandler.showErrorSnackBar(this, errorInfo, onRetry: onRetry);
    }
  }
}
