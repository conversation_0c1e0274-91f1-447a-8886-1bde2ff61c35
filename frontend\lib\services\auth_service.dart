import 'dart:convert';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:http/http.dart' as http;

import 'package:syra_security/core/exceptions.dart';
import 'package:syra_security/core/utils.dart';

/// Model untuk user data
class UserData {
  final String id;
  final String email;
  final String name;
  final String? photoUrl;
  final String? accessToken;

  const UserData({
    required this.id,
    required this.email,
    required this.name,
    this.photoUrl,
    this.accessToken,
  });

  factory UserData.fromJson(Map<String, dynamic> json) {
    return UserData(
      id: json['id'] ?? '',
      email: json['email'] ?? '',
      name: json['name'] ?? '',
      photoUrl: json['photoUrl'],
      accessToken: json['accessToken'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'email': email,
      'name': name,
      'photoUrl': photoUrl,
      'accessToken': accessToken,
    };
  }
}

/// Service untuk menangani autentikasi
class AuthService {
  static const _storage = FlutterSecureStorage();
  static const _userDataKey = 'user_data';
  static const _authTokenKey = 'auth_token';
  static const _refreshTokenKey = 'refresh_token';

  final GoogleSignIn _googleSignIn = GoogleSignIn(
    scopes: ['email', 'profile'],
    // Menggunakan Client ID yang diberikan dengan SHA-1 yang benar
    serverClientId: '591286217635-n8rtb6480ik7cr6s24m6q5ichg40c8ci.apps.googleusercontent.com',
  );

  final String baseUrl;
  final http.Client _client;

  AuthService({
    required this.baseUrl,
    http.Client? client,
  }) : _client = client ?? http.Client();

  /// Sign in dengan Google
  Future<UserData> signInWithGoogle() async {
    AppUtils.logInfo('Starting Google Sign In');

    try {
      // Sign out terlebih dahulu untuk memastikan fresh login
      await _googleSignIn.signOut();

      // Mulai proses sign in
      final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();

      if (googleUser == null) {
        throw AuthenticationException('Google Sign In dibatalkan oleh user');
      }

      AppUtils.logInfo('Google Sign In successful for: ${googleUser.email}');

      // Dapatkan authentication details
      final GoogleSignInAuthentication googleAuth = await googleUser.authentication;

      if (googleAuth.accessToken == null) {
        throw AuthenticationException('Gagal mendapatkan access token dari Google');
      }

      // Buat user data
      final userData = UserData(
        id: googleUser.id,
        email: googleUser.email,
        name: googleUser.displayName ?? '',
        photoUrl: googleUser.photoUrl,
        accessToken: googleAuth.accessToken,
      );

      // Verifikasi dengan backend dan dapatkan JWT token
      final jwtToken = await _verifyWithBackend(userData);

      // Simpan data user dan token
      await _saveUserData(userData);
      await _saveAuthToken(jwtToken);

      AppUtils.logInfo('Authentication completed successfully');
      return userData;

    } catch (e) {
      AppUtils.logError('Google Sign In failed', e);
      if (e is AuthenticationException) {
        rethrow;
      }
      throw AuthenticationException('Gagal melakukan sign in: $e');
    }
  }

  /// Verifikasi dengan backend dan dapatkan JWT token
  Future<String> _verifyWithBackend(UserData userData) async {
    try {
      AppUtils.logInfo('Verifying with backend for user: ${userData.email}');

      final response = await _client.post(
        Uri.parse('$baseUrl/api/auth/google'),
        headers: {
          'Content-Type': 'application/json',
        },
        body: json.encode({
          'googleToken': userData.accessToken,
          'email': userData.email,
          'name': userData.name,
          'photoUrl': userData.photoUrl,
        }),
      ).timeout(const Duration(seconds: 10));

      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        final jwtToken = responseData['token'];

        if (jwtToken == null) {
          throw AuthenticationException('Backend tidak mengembalikan JWT token');
        }

        AppUtils.logInfo('Backend verification successful');
        return jwtToken;
      } else {
        AppUtils.logError('Backend verification failed', 'Status: ${response.statusCode}');
        throw AuthenticationException('Verifikasi backend gagal: ${response.statusCode}');
      }
    } catch (e) {
      AppUtils.logWarning('Backend verification failed, using mock token: $e');
      // Fallback: gunakan mock token untuk development
      return _generateMockJwtToken(userData);
    }
  }

  /// Generate mock JWT token untuk development
  String _generateMockJwtToken(UserData userData) {
    final payload = {
      'sub': userData.id,
      'email': userData.email,
      'name': userData.name,
      'iat': DateTime.now().millisecondsSinceEpoch ~/ 1000,
      'exp': DateTime.now().add(const Duration(days: 7)).millisecondsSinceEpoch ~/ 1000,
    };
    
    // Ini adalah mock token untuk development
    // Dalam production, token harus dibuat oleh backend
    final mockToken = 'mock.${base64Encode(utf8.encode(json.encode(payload)))}.signature';
    AppUtils.logInfo('Generated mock JWT token');
    return mockToken;
  }

  /// Sign out
  Future<void> signOut() async {
    try {
      AppUtils.logInfo('Starting sign out process');
      
      // Sign out dari Google
      await _googleSignIn.signOut();
      
      // Hapus semua data tersimpan
      await _clearStoredData();
      
      AppUtils.logInfo('Sign out completed successfully');
    } catch (e) {
      AppUtils.logError('Error during sign out', e);
      // Tetap hapus data lokal meskipun Google sign out gagal
      await _clearStoredData();
    }
  }

  /// Cek apakah user sudah login
  Future<bool> isSignedIn() async {
    try {
      final userData = await getCurrentUser();
      final authToken = await getAuthToken();
      return userData != null && authToken != null;
    } catch (e) {
      AppUtils.logError('Error checking sign in status', e);
      return false;
    }
  }

  /// Dapatkan current user data
  Future<UserData?> getCurrentUser() async {
    try {
      final userDataString = await _storage.read(key: _userDataKey);
      if (userDataString == null) return null;
      
      final userDataJson = json.decode(userDataString);
      return UserData.fromJson(userDataJson);
    } catch (e) {
      AppUtils.logError('Error getting current user', e);
      return null;
    }
  }

  /// Dapatkan auth token
  Future<String?> getAuthToken() async {
    try {
      return await _storage.read(key: _authTokenKey);
    } catch (e) {
      AppUtils.logError('Error getting auth token', e);
      return null;
    }
  }

  /// Simpan user data
  Future<void> _saveUserData(UserData userData) async {
    try {
      final userDataString = json.encode(userData.toJson());
      await _storage.write(key: _userDataKey, value: userDataString);
      AppUtils.logInfo('User data saved successfully');
    } catch (e) {
      AppUtils.logError('Error saving user data', e);
      throw AuthenticationException('Gagal menyimpan data user');
    }
  }

  /// Simpan auth token
  Future<void> _saveAuthToken(String token) async {
    try {
      await _storage.write(key: _authTokenKey, value: token);
      AppUtils.logInfo('Auth token saved successfully');
    } catch (e) {
      AppUtils.logError('Error saving auth token', e);
      throw AuthenticationException('Gagal menyimpan auth token');
    }
  }

  /// Hapus semua data tersimpan
  Future<void> _clearStoredData() async {
    try {
      await _storage.delete(key: _userDataKey);
      await _storage.delete(key: _authTokenKey);
      await _storage.delete(key: _refreshTokenKey);
      AppUtils.logInfo('All stored data cleared');
    } catch (e) {
      AppUtils.logError('Error clearing stored data', e);
    }
  }

  /// Refresh auth token (untuk implementasi masa depan)
  Future<String?> refreshAuthToken() async {
    // TODO: Implementasi refresh token logic
    AppUtils.logInfo('Token refresh not implemented yet');
    return null;
  }
}
