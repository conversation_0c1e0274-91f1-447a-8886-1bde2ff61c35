import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:syra_security/config/theme.dart';
import 'package:syra_security/features/domains/data/models/domain_model.dart';
import 'package:syra_security/features/domains/presentation/widgets/detected_attacks_tab.dart';
import 'package:syra_security/features/domains/presentation/widgets/security_log_tab.dart';
import 'package:syra_security/features/domains/presentation/providers/domain_provider.dart';
import 'package:syra_security/widgets/syra_logo_widget.dart';

class DomainDetailScreen extends ConsumerStatefulWidget {
  final String domainId;

  const DomainDetailScreen({
    super.key,
    required this.domainId,
  });

  @override
  ConsumerState<DomainDetailScreen> createState() => _DomainDetailScreenState();
}

class _DomainDetailScreenState extends ConsumerState<DomainDetailScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    debugPrint('DomainDetailScreen build dipanggil dengan id: ${widget.domainId}');
    final domainAsync = ref.watch(domainProvider(widget.domainId));
    
    return domainAsync.when(
      data: (domain) => _buildDomainDetail(context, domain),
      loading: () => Scaffold(
        appBar: AppBar(
          title: const Text('Loading...'),
        ),
        body: const Center(
          child: CircularProgressIndicator(),
        ),
      ),
      error: (error, stack) => Scaffold(
        appBar: AppBar(
          title: const Text('Error'),
        ),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.error, size: 64, color: Colors.red),
              const SizedBox(height: 16),
              Text('Error: $error'),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () {
                  if (context.canPop()) {
                    context.pop();
                  } else {
                    context.go('/dashboard');
                  }
                },
                child: const Text('Kembali'),
              ),
            ],
          ),
        ),
      ),
    );
  }
  
  Widget _buildDomainDetail(BuildContext context, Domain domain) {

    return Scaffold(
      appBar: AppBar(
        title: const Text('Detail Domain'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            debugPrint('Back button pressed from domain detail');
            try {
              if (context.canPop()) {
                context.pop();
              } else {
                context.go('/dashboard');
              }
            } catch (e) {
              debugPrint('Error navigating back: $e');
              context.go('/dashboard');
            }
          },
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.delete_outline),
            onPressed: () => _showDeleteDialog(context, domain),
          ),
        ],
      ),
      body: Column(
        children: [
          // Domain header - Match UI design
          Container(
            padding: const EdgeInsets.all(24),
            child: Column(
              children: [
                // Domain logo - menggunakan SyraLogoWidget yang sudah diperbaiki
                const SyraLogoWidget(
                  size: 100,
                  backgroundColor: Color(0xFF1565C0),
                  foregroundColor: Colors.white,
                  showShadow: false,
                ),
                const SizedBox(height: 16),

                // Domain URL
                Text(
                  domain.url,
                  style: const TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),

                // Created and updated dates
                Text(
                  'Created At: ${DateFormat('d MMM yyyy').format(domain.createdAt)}',
                  style: const TextStyle(
                    fontSize: 14,
                    color: Colors.grey,
                  ),
                ),
                Text(
                  'Last Update At: ${DateFormat('d MMM yyyy').format(domain.updatedAt)}',
                  style: const TextStyle(
                    fontSize: 14,
                    color: Colors.grey,
                  ),
                ),
                const SizedBox(height: 24),

                // Attack stats - Horizontal layout to match UI
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    _buildAttackStat('${domain.attackStats.ddos}', 'DDoS'),
                    _buildAttackStat('${domain.attackStats.sqlInjection}', 'SQL Injection'),
                    _buildAttackStat('${domain.attackStats.xss}', 'XSS'),
                  ],
                ),
              ],
            ),
          ),
          
          // Tab bar - Match UI design
          Container(
            decoration: const BoxDecoration(
              border: Border(
                bottom: BorderSide(color: Colors.grey, width: 0.5),
              ),
            ),
            child: TabBar(
              controller: _tabController,
              tabs: const [
                Tab(text: 'Security Log'),
                Tab(text: 'Detected Attacks'),
              ],
              labelColor: const Color(0xFF1976D2),
              unselectedLabelColor: Colors.grey,
              indicatorColor: const Color(0xFF1976D2),
            ),
          ),
          
          // Tab content
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                SecurityLogTab(logs: domain.securityLogs),
                DetectedAttacksTab(attacks: domain.detectedAttacks),
              ],
            ),
          ),
          
          // Tombol kembali di bagian bawah
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: ElevatedButton.icon(
              onPressed: () {
                debugPrint('Tombol kembali di bagian bawah diklik');
                try {
                  context.go('/dashboard');
                  debugPrint('Navigasi ke dashboard berhasil');
                } catch (e) {
                  debugPrint('Error navigasi ke dashboard: $e');
                }
              },
              icon: const Icon(Icons.arrow_back),
              label: const Text('Kembali ke Dashboard'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.secondaryColor,
                foregroundColor: Colors.white,
                minimumSize: const Size(double.infinity, 48),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAttackStat(String count, String label) {
    return Column(
      children: [
        Text(
          count,
          style: const TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: const TextStyle(
            color: Colors.grey,
            fontSize: 12,
          ),
        ),
      ],
    );
  }

  void _showDeleteDialog(BuildContext context, Domain domain) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Hapus Domain'),
        content: Text('Apakah Anda yakin ingin menghapus ${domain.url}?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Batal'),
          ),
          TextButton(
            onPressed: () async {
              final navigator = Navigator.of(context);
              final messenger = ScaffoldMessenger.of(context);

              navigator.pop();

              debugPrint('Attempting to delete domain: ${domain.id}');

              try {
                await ref.read(domainListProvider.notifier).deleteDomain(domain.id);

                debugPrint('Domain deleted successfully');

                if (mounted) {
                  messenger.showSnackBar(
                    const SnackBar(
                      content: Text('Domain berhasil dihapus'),
                      backgroundColor: Colors.green,
                    ),
                  );

                  // Refresh domain list after deletion
                  await ref.read(domainListProvider.notifier).loadDomains();

                  // User can manually navigate back
                }
              } catch (e) {
                debugPrint('Error deleting domain: $e');
                if (mounted) {
                  messenger.showSnackBar(
                    SnackBar(
                      content: Text('Gagal menghapus domain: $e'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              }
            },
            child: const Text('Hapus', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }
}