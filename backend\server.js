const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const { v4: uuidv4 } = require('uuid');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(helmet());
app.use(cors());
app.use(morgan('combined'));
app.use(express.json());

// In-memory database (untuk demo)
let domains = [
  {
    id: uuidv4(),
    url: 'trenteknologimobile.com',
    createdAt: new Date('2025-05-01'),
    updatedAt: new Date('2025-05-02'),
    attackStats: {
      ddos: 0,
      sqlInjection: 0,
      xss: 0
    },
    securityLogs: [
      {
        id: uuidv4(),
        timestamp: new Date('2025-05-11T10:22:47Z'),
        message: 'No threats detected - normal activity for trenteknologimobile.com',
        isAlert: false
      },
      {
        id: uuidv4(),
        timestamp: new Date('2025-05-11T10:22:46Z'),
        message: 'No threats detected - normal activity for trenteknologimobile.com',
        isAlert: false
      },
      {
        id: uuidv4(),
        timestamp: new Date('2025-05-11T10:22:43Z'),
        message: 'Clean traffic observed - no suspicious activity detected',
        isAlert: false
      }
    ],
    detectedAttacks: [
      {
        id: uuidv4(),
        type: 'sqlInjection',
        severity: 'Medium',
        detectedAt: new Date('2025-05-11T10:22:45Z'),
        resolvedAt: new Date('2025-05-11T10:23:30Z'),
        details: 'The system has automatically blocked a malicious IP and isolated affected components to prevent the spread of the attack',
        mitigationStatus: 'Resolved'
      }
    ]
  },
  {
    id: uuidv4(),
    url: 'proyekutamainformatika.com',
    createdAt: new Date('2025-05-01'),
    updatedAt: new Date('2025-05-02'),
    attackStats: {
      ddos: 0,
      sqlInjection: 0,
      xss: 0
    },
    securityLogs: [
      {
        id: uuidv4(),
        timestamp: new Date('2025-05-11T10:22:47Z'),
        message: 'No threats detected - normal activity for proyekutamainformatika.com',
        isAlert: false
      },
      {
        id: uuidv4(),
        timestamp: new Date('2025-05-10T10:22:46Z'),
        message: 'No threats detected - normal activity for proyekutamainformatika.com',
        isAlert: false
      },
      {
        id: uuidv4(),
        timestamp: new Date('2025-05-11T10:22:43Z'),
        message: 'Clean traffic observed - no suspicious activity detected',
        isAlert: false
      }
    ],
    detectedAttacks: []
  }
];

// Routes

// Health check
app.get('/health', (req, res) => {
  res.json({ 
    status: 'OK', 
    message: 'SYRA Security Backend is running',
    timestamp: new Date().toISOString()
  });
});

// Get all domains
app.get('/api/domains', (req, res) => {
  console.log('GET /api/domains - Returning domains:', domains.length);
  res.json({
    success: true,
    data: domains,
    count: domains.length
  });
});

// Get domain by ID
app.get('/api/domains/:id', (req, res) => {
  const { id } = req.params;
  const domain = domains.find(d => d.id === id);
  
  if (!domain) {
    return res.status(404).json({
      success: false,
      message: 'Domain not found'
    });
  }
  
  console.log(`GET /api/domains/${id} - Found domain:`, domain.url);
  res.json({
    success: true,
    data: domain
  });
});

// Add new domain
app.post('/api/domains', (req, res) => {
  const { url } = req.body;
  
  if (!url) {
    return res.status(400).json({
      success: false,
      message: 'URL is required'
    });
  }
  
  // Check if domain already exists
  const existingDomain = domains.find(d => d.url === url);
  if (existingDomain) {
    return res.status(409).json({
      success: false,
      message: 'Domain already exists'
    });
  }
  
  const newDomain = {
    id: uuidv4(),
    url: url,
    createdAt: new Date(),
    updatedAt: new Date(),
    attackStats: {
      ddos: 0,
      sqlInjection: 0,
      xss: 0
    },
    securityLogs: [
      {
        id: uuidv4(),
        timestamp: new Date(),
        message: `Domain ${url} added to monitoring system`,
        isAlert: false
      }
    ],
    detectedAttacks: []
  };
  
  domains.push(newDomain);
  
  console.log('POST /api/domains - Added new domain:', url);
  res.status(201).json({
    success: true,
    data: newDomain,
    message: 'Domain added successfully'
  });
});

// Delete domain
app.delete('/api/domains/:id', (req, res) => {
  const { id } = req.params;
  const domainIndex = domains.findIndex(d => d.id === id);
  
  if (domainIndex === -1) {
    return res.status(404).json({
      success: false,
      message: 'Domain not found'
    });
  }
  
  const deletedDomain = domains.splice(domainIndex, 1)[0];
  
  console.log('DELETE /api/domains/' + id + ' - Deleted domain:', deletedDomain.url);
  res.json({
    success: true,
    message: 'Domain deleted successfully',
    data: deletedDomain
  });
});

// Simulate attack detection (for demo purposes)
app.post('/api/domains/:id/simulate-attack', (req, res) => {
  const { id } = req.params;
  const { type } = req.body; // 'ddos', 'sqlInjection', 'xss'
  
  const domain = domains.find(d => d.id === id);
  if (!domain) {
    return res.status(404).json({
      success: false,
      message: 'Domain not found'
    });
  }
  
  // Add attack to stats
  if (type === 'ddos') domain.attackStats.ddos++;
  else if (type === 'sqlInjection') domain.attackStats.sqlInjection++;
  else if (type === 'xss') domain.attackStats.xss++;
  
  // Add to detected attacks
  const newAttack = {
    id: uuidv4(),
    type: type,
    severity: 'Medium',
    detectedAt: new Date(),
    resolvedAt: null,
    details: `Simulated ${type} attack detected and blocked automatically`,
    mitigationStatus: 'Detected'
  };
  
  domain.detectedAttacks.unshift(newAttack);
  domain.updatedAt = new Date();
  
  // Add security log
  domain.securityLogs.unshift({
    id: uuidv4(),
    timestamp: new Date(),
    message: `${type.toUpperCase()} attack detected from suspicious IP`,
    isAlert: true
  });
  
  console.log(`POST /api/domains/${id}/simulate-attack - Simulated ${type} attack`);
  res.json({
    success: true,
    message: 'Attack simulated successfully',
    data: domain
  });
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error('Error:', err);
  res.status(500).json({
    success: false,
    message: 'Internal server error',
    error: process.env.NODE_ENV === 'development' ? err.message : undefined
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: 'Route not found'
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 SYRA Security Backend running on port ${PORT}`);
  console.log(`📊 Health check: http://localhost:${PORT}/health`);
  console.log(`🔗 API Base URL: http://localhost:${PORT}/api`);
  console.log(`📱 Ready to serve mobile app requests!`);
});

module.exports = app;
