import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:syra_security/main.dart';

void main() {
  group('SYRA Security App Tests', () {
    testWidgets('App should build without errors', (WidgetTester tester) async {
      // Build our app and trigger a frame.
      await tester.pumpWidget(const ProviderScope(child: SyraApp()));
      await tester.pump();

      // Verify that the app builds successfully
      expect(find.byType(MaterialApp), findsOneWidget);
    });

    testWidgets('App should show login screen initially', (WidgetTester tester) async {
      // Build our app and trigger a frame.
      await tester.pumpWidget(const ProviderScope(child: SyraApp()));
      await tester.pump();

      // Should show login screen elements
      expect(find.text('SYRA'), findsOneWidget);
      expect(find.text('Secure Your Realm Always'), findsOneWidget);
      expect(find.text('Sign in with <PERSON>'), findsOneWidget);
    });

    testWidgets('App should handle navigation correctly', (WidgetTester tester) async {
      // Build our app and trigger a frame.
      await tester.pumpWidget(const ProviderScope(child: SyraApp()));
      await tester.pump();

      // Verify initial route is login
      expect(find.text('SYRA'), findsOneWidget);

      // Test that the app doesn't crash on navigation attempts
      // (Actual navigation testing would require more complex setup)
    });
  });
}
