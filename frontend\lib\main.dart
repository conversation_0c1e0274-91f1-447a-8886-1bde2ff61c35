import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:syra_security/config/router.dart';
import 'package:syra_security/config/theme.dart';
import 'package:syra_security/core/utils.dart';
import 'package:syra_security/core/widgets/common_widgets.dart';

/// Entry point aplikasi
Future<void> main() async {
  // Menangkap error dari zona asynchronous
  runZonedGuarded(() async {
    WidgetsFlutterBinding.ensureInitialized();

    AppUtils.logInfo('Application started');

    runApp(
      const ProviderScope(
        child: SyraApp(),
      ),
    );
  }, (error, stackTrace) {
    AppUtils.logError('Uncaught exception', error, stackTrace);
  });
}

/// Widget utama aplikasi
class SyraApp extends ConsumerWidget {
  const SyraApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    AppUtils.logInfo('Building SyraApp');

    try {
      final router = createRouter(ref);

      return MaterialApp.router(
        title: 'SYRA Security',
        theme: AppTheme.lightTheme,
        routerConfig: router,
        debugShowCheckedModeBanner: false,

      );
    } catch (e, stackTrace) {
      AppUtils.logError('Error building application', e, stackTrace);
      // Fallback UI jika terjadi error
      return const MaterialApp(
        home: Scaffold(
          body: ErrorDisplay(
            message: 'Terjadi kesalahan saat memulai aplikasi',
            icon: Icons.error_outline,
            color: Colors.red,
          ),
        ),
      );
    }
  }
}

