import 'package:flutter/material.dart';

/// Widget untuk menampilkan logo SYRA yang dapat digunakan di berbagai tempat
class SyraLogoWidget extends StatelessWidget {
  final double size;
  final Color backgroundColor;
  final Color foregroundColor;
  final bool showShadow;

  const SyraLogoWidget({
    super.key,
    this.size = 120,
    this.backgroundColor = const Color(0xFF1565C0),
    this.foregroundColor = Colors.white,
    this.showShadow = true,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: backgroundColor,
        shape: BoxShape.circle,
        boxShadow: showShadow ? [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.2),
            blurRadius: size * 0.1,
            offset: Offset(0, size * 0.05),
          ),
        ] : null,
      ),
      child: Center(
        child: CustomPaint(
          size: Size(size * 0.5, size * 0.5),
          painter: <PERSON><PERSON><PERSON><PERSON>Pain<PERSON>(color: foregroundColor),
        ),
      ),
    );
  }
}

/// Custom Painter untuk logo SYRA yang sesuai dengan gambar yang diberikan
class SyraSLogoPainter extends CustomPainter {
  final Color color;

  SyraSLogoPainter({this.color = Colors.white});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill
      ..strokeCap = StrokeCap.round
      ..strokeJoin = StrokeJoin.round;

    final width = size.width;
    final height = size.height;
    final centerX = width / 2;
    final centerY = height / 2;

    // Membuat huruf "S" yang stylized sesuai gambar yang diberikan
    final path = Path();

    // Bagian atas "S" - kurva melengkung seperti di gambar
    path.moveTo(centerX + width * 0.25, centerY - height * 0.35);
    path.cubicTo(
      centerX + width * 0.35, centerY - height * 0.45,
      centerX - width * 0.1, centerY - height * 0.45,
      centerX - width * 0.25, centerY - height * 0.25
    );
    path.cubicTo(
      centerX - width * 0.35, centerY - height * 0.15,
      centerX - width * 0.25, centerY - height * 0.05,
      centerX - width * 0.05, centerY - height * 0.05
    );

    // Bagian tengah - transisi
    path.cubicTo(
      centerX + width * 0.15, centerY - height * 0.05,
      centerX + width * 0.25, centerY + height * 0.05,
      centerX + width * 0.35, centerY + height * 0.15
    );
    path.cubicTo(
      centerX + width * 0.25, centerY + height * 0.25,
      centerX + width * 0.1, centerY + height * 0.45,
      centerX - width * 0.35, centerY + height * 0.45
    );

    // Bagian bawah "S" - kurva melengkung terbalik
    path.cubicTo(
      centerX - width * 0.25, centerY + height * 0.35,
      centerX - width * 0.35, centerY + height * 0.45,
      centerX - width * 0.25, centerY + height * 0.35
    );

    // Gambar "S" dengan fill
    canvas.drawPath(path, paint);

    // Tambahkan detail untuk membuat "S" lebih stylized seperti di gambar

    // Bagian atas melengkung
    final topCurve = Path();
    topCurve.moveTo(centerX - width * 0.2, centerY - height * 0.3);
    topCurve.cubicTo(
      centerX - width * 0.3, centerY - height * 0.35,
      centerX - width * 0.3, centerY - height * 0.2,
      centerX - width * 0.15, centerY - height * 0.15
    );
    topCurve.cubicTo(
      centerX - width * 0.05, centerY - height * 0.1,
      centerX + width * 0.05, centerY - height * 0.1,
      centerX + width * 0.15, centerY - height * 0.15
    );
    topCurve.cubicTo(
      centerX + width * 0.25, centerY - height * 0.2,
      centerX + width * 0.2, centerY - height * 0.3,
      centerX + width * 0.1, centerY - height * 0.25
    );
    canvas.drawPath(topCurve, paint);

    // Bagian bawah melengkung
    final bottomCurve = Path();
    bottomCurve.moveTo(centerX + width * 0.2, centerY + height * 0.3);
    bottomCurve.cubicTo(
      centerX + width * 0.3, centerY + height * 0.35,
      centerX + width * 0.3, centerY + height * 0.2,
      centerX + width * 0.15, centerY + height * 0.15
    );
    bottomCurve.cubicTo(
      centerX + width * 0.05, centerY + height * 0.1,
      centerX - width * 0.05, centerY + height * 0.1,
      centerX - width * 0.15, centerY + height * 0.15
    );
    bottomCurve.cubicTo(
      centerX - width * 0.25, centerY + height * 0.2,
      centerX - width * 0.2, centerY + height * 0.3,
      centerX - width * 0.1, centerY + height * 0.25
    );
    canvas.drawPath(bottomCurve, paint);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}
