import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

class AppConfig {
  static const String appName = 'SYRA Security';
  static const String appVersion = '1.0.0';
  static const String buildNumber = '1';
  
  // Environment Configuration
  static bool get isProduction => kReleaseMode;
  static bool get isDevelopment => kDebugMode;
  static bool get isProfile => kProfileMode;
  
  // API Configuration
  static String get baseUrl {
    if (isProduction) {
      return 'https://api.syrasecurity.com';
    } else {
      return 'https://dev-api.syrasecurity.com';
    }
  }
  
  // Security Configuration
  static const Duration sessionTimeout = Duration(hours: 24);
  static const Duration refreshTokenThreshold = Duration(minutes: 5);
  static const int maxRetryAttempts = 3;
  static const Duration retryDelay = Duration(seconds: 2);
  
  // UI Configuration
  static const Duration animationDuration = Duration(milliseconds: 300);
  static const Duration snackBarDuration = Duration(seconds: 3);
  static const Duration loadingTimeout = Duration(seconds: 30);
  
  // Security Monitoring Configuration
  static const Duration securityScanInterval = Duration(minutes: 5);
  static const Duration realTimeUpdateInterval = Duration(seconds: 30);
  static const int maxSecurityLogsPerPage = 50;
  static const int maxDetectedAttacksPerPage = 20;
  
  // Cache Configuration
  static const Duration cacheExpiration = Duration(hours: 1);
  static const int maxCacheSize = 100; // MB
  
  // Logging Configuration
  static bool get enableLogging => isDevelopment || isProfile;
  static bool get enableCrashReporting => isProduction;
  static bool get enableAnalytics => isProduction;
  
  // Feature Flags
  static bool get enableRealTimeUpdates => true;
  static bool get enablePushNotifications => true;
  static bool get enableOfflineMode => true;
  static bool get enableDarkMode => true;
  static bool get enableBiometricAuth => false; // Future feature
  
  // Google Services Configuration
  static String get googleClientId {
    if (isProduction) {
      return 'your-production-google-client-id';
    } else {
      return 'your-development-google-client-id';
    }
  }
  
  // Firebase Configuration
  static String get firebaseProjectId {
    if (isProduction) {
      return 'syra-security-prod';
    } else {
      return 'syra-security-dev';
    }
  }
  
  // App Store Configuration
  static const String playStoreUrl = 'https://play.google.com/store/apps/details?id=com.syrasecurity.app';
  static const String appStoreUrl = 'https://apps.apple.com/app/syra-security/id123456789';
  static const String privacyPolicyUrl = 'https://syrasecurity.com/privacy';
  static const String termsOfServiceUrl = 'https://syrasecurity.com/terms';
  static const String supportEmail = '<EMAIL>';
  static const String supportUrl = 'https://syrasecurity.com/support';
  
  // Social Media
  static const String websiteUrl = 'https://syrasecurity.com';
  static const String twitterUrl = 'https://twitter.com/syrasecurity';
  static const String linkedinUrl = 'https://linkedin.com/company/syrasecurity';
  
  // Development Configuration
  static bool get showDebugBanner => isDevelopment;
  static bool get enableInspector => isDevelopment;
  static bool get enablePerformanceOverlay => false;
  
  // Security Headers
  static Map<String, String> get defaultHeaders => {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    'User-Agent': '$appName/$appVersion',
    'X-App-Version': appVersion,
    'X-Platform': 'mobile',
  };
  
  // Error Messages
  static const String networkErrorMessage = 'Please check your internet connection and try again.';
  static const String serverErrorMessage = 'Server is temporarily unavailable. Please try again later.';
  static const String authErrorMessage = 'Authentication failed. Please sign in again.';
  static const String permissionErrorMessage = 'Permission denied. Please check your account permissions.';
  static const String validationErrorMessage = 'Please check your input and try again.';
  static const String unknownErrorMessage = 'An unexpected error occurred. Please try again.';
  
  // Success Messages
  static const String domainAddedMessage = 'Domain added successfully!';
  static const String domainDeletedMessage = 'Domain deleted successfully!';
  static const String domainUpdatedMessage = 'Domain updated successfully!';
  static const String loginSuccessMessage = 'Welcome back!';
  static const String logoutSuccessMessage = 'Signed out successfully!';
  
  // Validation Rules
  static const int minDomainLength = 3;
  static const int maxDomainLength = 253;
  static const int maxDomainsPerUser = 50;
  
  // Performance Thresholds
  static const Duration maxApiResponseTime = Duration(seconds: 10);
  static const double maxMemoryUsage = 150.0; // MB
  static const double maxCpuUsage = 80.0; // Percentage
  
  // Accessibility
  static const double minTouchTargetSize = 44.0;
  static const double defaultFontSize = 16.0;
  static const double largeFontSize = 20.0;
  static const double smallFontSize = 12.0;
  
  // Animation Curves
  static const Curve defaultCurve = Curves.easeInOut;
  static const Curve fastCurve = Curves.easeOut;
  static const Curve slowCurve = Curves.easeIn;
  
  // Color Opacity Values
  static const double disabledOpacity = 0.38;
  static const double hoverOpacity = 0.04;
  static const double focusOpacity = 0.12;
  static const double selectedOpacity = 0.08;
  
  // Z-Index Values
  static const double appBarElevation = 4.0;
  static const double cardElevation = 2.0;
  static const double dialogElevation = 24.0;
  static const double snackBarElevation = 6.0;
  
  // Border Radius Values
  static const double smallRadius = 4.0;
  static const double mediumRadius = 8.0;
  static const double largeRadius = 16.0;
  static const double circularRadius = 50.0;
  
  // Spacing Values
  static const double smallSpacing = 8.0;
  static const double mediumSpacing = 16.0;
  static const double largeSpacing = 24.0;
  static const double extraLargeSpacing = 32.0;
  
  // Icon Sizes
  static const double smallIconSize = 16.0;
  static const double mediumIconSize = 24.0;
  static const double largeIconSize = 32.0;
  static const double extraLargeIconSize = 48.0;
  
  // Button Heights
  static const double smallButtonHeight = 32.0;
  static const double mediumButtonHeight = 48.0;
  static const double largeButtonHeight = 56.0;
  
  // Input Field Heights
  static const double inputFieldHeight = 56.0;
  static const double compactInputFieldHeight = 40.0;
  
  // List Item Heights
  static const double listItemHeight = 72.0;
  static const double compactListItemHeight = 56.0;
  static const double denseListItemHeight = 48.0;
}
