import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:syra_security/config/theme.dart';
import 'package:syra_security/features/domains/data/models/domain_model.dart';

class DomainCard extends ConsumerWidget {
  final Domain domain;

  const DomainCard({
    super.key,
    required this.domain,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Variabel hasAlerts digunakan untuk menentukan status domain (tidak digunakan saat ini)
    // final hasAlerts = domain.securityLogs.any((log) => log.isAlert);
    final latestAttack = domain.detectedAttacks.isNotEmpty
        ? domain.detectedAttacks.first
        : null;

    return Card(
      elevation: 1,
      margin: EdgeInsets.zero,
      child: InkWell(
        onTap: () => context.go('/domain/${domain.id}'),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    width: 50,
                    height: 50,
                    decoration: const BoxDecoration(
                      color: Color(0xFF1565C0), // Blue sesuai gambar
                      shape: BoxShape.circle,
                    ),
                    child: const Center(
                      child: Text(
                        "S",
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      domain.url,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              
              const Text(
                'Security Log:',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 8),
              
              // Show up to 3 latest logs
              ...domain.securityLogs.take(3).map((log) => Padding(
                padding: const EdgeInsets.only(bottom: 4.0),
                child: RichText(
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  text: TextSpan(
                    children: [
                      TextSpan(
                        text: '[${DateFormat('yyyy-MM-dd HH:mm:ss').format(log.timestamp)}] ',
                        style: TextStyle(
                          color: log.isAlert ? AppTheme.errorColor : AppTheme.textLight,
                          fontFamily: 'Poppins',
                          fontSize: 12,
                        ),
                      ),
                      TextSpan(
                        text: log.message,
                        style: TextStyle(
                          color: log.isAlert ? AppTheme.errorColor : AppTheme.textDark,
                          fontFamily: 'Poppins',
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),
              )),
              
              // Show latest attack alert if any
              if (latestAttack != null) ...[
                const SizedBox(height: 16),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: AppTheme.errorColor.withAlpha(25),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: const BoxDecoration(
                          color: AppTheme.errorColor,
                          shape: BoxShape.circle,
                        ),
                        child: const Icon(
                          Icons.close,
                          color: Colors.white,
                          size: 16,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              '${_getAttackTypeLabel(latestAttack.type)} Detected',
                              style: const TextStyle(
                                fontWeight: FontWeight.w600,
                                fontSize: 14,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              latestAttack.details,
                              style: const TextStyle(
                                fontSize: 12,
                              ),
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
  
  String _getAttackTypeLabel(AttackType type) {
    switch (type) {
      case AttackType.ddos:
        return 'DDoS';
      case AttackType.sqlInjection:
        return 'SQL Injection';
      case AttackType.xss:
        return 'XSS';
    }
  }
}