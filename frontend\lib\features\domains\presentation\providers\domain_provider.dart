import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:syra_security/core/constants.dart';
import 'package:syra_security/core/exceptions.dart';
import 'package:syra_security/core/utils.dart';
import 'package:syra_security/features/domains/data/models/domain_model.dart';
import 'package:syra_security/services/api_service.dart';
import 'package:syra_security/services/auth_service.dart';
import 'package:syra_security/services/websocket_service.dart';
import 'package:syra_security/services/error_handler_service.dart';
import 'package:syra_security/services/security_scanner_service.dart';

/// Provider untuk AuthService
final authServiceProvider = Provider<AuthService>((ref) {
  return AuthService(
    baseUrl: AppConstants.baseApiUrl,
  );
});

/// Provider untuk ApiService
final apiServiceProvider = Provider<ApiService>((ref) {
  // Gunakan backend Node.js di port 3000
  return ApiService(
    baseUrl: AppConstants.baseApiUrl,
    useMockData: false, // Gunakan backend yang sudah dibuat
  );
});

/// Provider untuk WebSocketService
final webSocketServiceProvider = Provider<WebSocketService>((ref) {
  return WebSocketService(
    baseUrl: AppConstants.baseApiUrl,
  );
});

/// Provider untuk ErrorHandlerService
final errorHandlerServiceProvider = Provider<ErrorHandlerService>((ref) {
  return ErrorHandlerService();
});

/// Provider untuk SecurityScannerService
final securityScannerServiceProvider = Provider<SecurityScannerService>((ref) {
  return SecurityScannerService(
    baseUrl: AppConstants.baseApiUrl,
  );
});

/// State untuk daftar domain
class DomainListState {
  final List<Domain> domains;
  final bool isLoading;
  final String? error;

  const DomainListState({
    this.domains = const [],
    this.isLoading = false,
    this.error,
  });

  /// Membuat salinan state dengan nilai yang diperbarui
  DomainListState copyWith({
    List<Domain>? domains,
    bool? isLoading,
    String? error,
  }) {
    return DomainListState(
      domains: domains ?? this.domains,
      isLoading: isLoading ?? this.isLoading,
      error: error,
    );
  }
}

/// Notifier untuk mengelola state daftar domain
class DomainListNotifier extends StateNotifier<DomainListState> {
  final ApiService _apiService;

  DomainListNotifier(this._apiService) : super(const DomainListState()) {
    loadDomains();
  }

  /// Memuat daftar domain dari API
  Future<void> loadDomains() async {
    state = state.copyWith(isLoading: true, error: null);
    
    try {
      final domains = await _apiService.getDomains();
      state = state.copyWith(domains: domains, isLoading: false);
      AppUtils.logInfo('Domains loaded: ${domains.length} domains');
    } catch (e) {
      final errorMessage = e is AppException ? e.message : e.toString();
      AppUtils.logError('Error loading domains', e);
      state = state.copyWith(error: errorMessage, isLoading: false);
    }
  }

  /// Menambahkan domain baru
  Future<void> addDomain(String url) async {
    AppUtils.logInfo('Adding domain: $url');
    state = state.copyWith(isLoading: true, error: null);
    
    try {
      final newDomain = await _apiService.addDomain(url);
      AppUtils.logInfo('Domain added successfully: ${newDomain.id} - ${newDomain.url}');
      
      // Tambahkan domain baru ke state
      final updatedDomains = [...state.domains, newDomain];
      state = state.copyWith(
        domains: updatedDomains,
        isLoading: false,
      );
      
      AppUtils.logInfo('State updated with new domain. Total domains: ${updatedDomains.length}');
    } catch (e) {
      final errorMessage = e is AppException ? e.message : e.toString();
      AppUtils.logError('Error adding domain', e);
      state = state.copyWith(error: errorMessage, isLoading: false);
      rethrow;
    }
  }

  /// Menghapus domain berdasarkan ID
  Future<void> deleteDomain(String id) async {
    AppUtils.logInfo('Deleting domain: $id');
    state = state.copyWith(isLoading: true, error: null);
    
    try {
      await _apiService.deleteDomain(id);
      
      // Perbarui state dengan menghapus domain yang dihapus
      final updatedDomains = state.domains.where((domain) => domain.id != id).toList();
      state = state.copyWith(
        domains: updatedDomains,
        isLoading: false,
      );
      
      AppUtils.logInfo('Domain deleted successfully. Remaining domains: ${updatedDomains.length}');
    } catch (e) {
      final errorMessage = e is AppException ? e.message : e.toString();
      AppUtils.logError('Error deleting domain', e);
      state = state.copyWith(error: errorMessage, isLoading: false);
      rethrow;
    }
  }

  /// Membersihkan error
  void clearError() {
    state = state.copyWith(error: null);
  }
}

/// Provider untuk daftar domain
final domainListProvider = StateNotifierProvider<DomainListNotifier, DomainListState>((ref) {
  final apiService = ref.watch(apiServiceProvider);
  return DomainListNotifier(apiService);
});

/// Provider untuk detail domain berdasarkan ID
final domainProvider = FutureProvider.family<Domain, String>((ref, id) async {
  final apiService = ref.watch(apiServiceProvider);
  return await apiService.getDomain(id);
});

/// State untuk autentikasi
class AuthState {
  final bool isAuthenticated;
  final UserData? userData;
  final bool isLoading;
  final String? error;

  const AuthState({
    this.isAuthenticated = false,
    this.userData,
    this.isLoading = false,
    this.error,
  });

  /// Membuat salinan state dengan nilai yang diperbarui
  AuthState copyWith({
    bool? isAuthenticated,
    UserData? userData,
    bool? isLoading,
    String? error,
  }) {
    return AuthState(
      isAuthenticated: isAuthenticated ?? this.isAuthenticated,
      userData: userData ?? this.userData,
      isLoading: isLoading ?? this.isLoading,
      error: error,
    );
  }

  /// Helper getter untuk email
  String? get userEmail => userData?.email;

  /// Helper getter untuk name
  String? get userName => userData?.name;
}

/// Notifier untuk mengelola state autentikasi
class AuthNotifier extends StateNotifier<AuthState> {
  final AuthService _authService;

  AuthNotifier(this._authService) : super(const AuthState()) {
    _checkInitialAuthState();
  }

  /// Cek status autentikasi saat startup
  Future<void> _checkInitialAuthState() async {
    try {
      AppUtils.logInfo('Checking initial auth state');
      final isSignedIn = await _authService.isSignedIn();

      if (isSignedIn) {
        final userData = await _authService.getCurrentUser();
        if (userData != null) {
          state = state.copyWith(
            isAuthenticated: true,
            userData: userData,
          );
          AppUtils.logInfo('User already signed in: ${userData.email}');
        }
      }
    } catch (e) {
      AppUtils.logError('Error checking initial auth state', e);
    }
  }

  /// Login dengan Google
  Future<void> signInWithGoogle() async {
    AppUtils.logInfo('Attempting to sign in with Google');
    state = state.copyWith(isLoading: true, error: null);

    try {
      final userData = await _authService.signInWithGoogle();
      state = state.copyWith(
        isAuthenticated: true,
        userData: userData,
        isLoading: false,
      );
      AppUtils.logInfo('Successfully signed in with Google: ${userData.email}');
    } catch (e) {
      AppUtils.logError('Error signing in with Google', e);
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
      rethrow;
    }
  }

  /// Logout
  Future<void> signOut() async {
    try {
      AppUtils.logInfo('Signing out user: ${state.userEmail}');
      await _authService.signOut();
      state = const AuthState();
      AppUtils.logInfo('User signed out successfully');
    } catch (e) {
      AppUtils.logError('Error during sign out', e);
      // Tetap reset state meskipun ada error
      state = const AuthState();
    }
  }

  /// Clear error state
  void clearError() {
    state = state.copyWith(error: null);
  }
}

/// Provider untuk autentikasi
final authProvider = StateNotifierProvider<AuthNotifier, AuthState>((ref) {
  final authService = ref.read(authServiceProvider);
  return AuthNotifier(authService);
});