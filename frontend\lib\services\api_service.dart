import 'dart:convert';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:http/http.dart' as http;
import 'package:syra_security/core/constants.dart';
import 'package:syra_security/core/exceptions.dart';
import 'package:syra_security/core/utils.dart';
import 'package:syra_security/features/domains/data/models/domain_model.dart';

/// Service untuk menangani semua komunikasi API
class ApiService {
  final String baseUrl;
  final http.Client _client;
  final bool _useMockData;

  ApiService({
    required this.baseUrl,
    http.Client? client,
    bool useMockData = false,
  }) : 
    _client = client ?? http.Client(),
    _useMockData = useMockData;

  /// Headers untuk autentikasi dan request API
  Future<Map<String, String>> get _headers async => {
    'Content-Type': 'application/json',
    if (await _getAuthToken() != null)
      'Authorization': 'Bearer ${await _getAuthToken()}',
  };

  /// Mendapatkan token autentikasi dari secure storage
  Future<String?> _getAuthToken() async {
    try {
      const storage = FlutterSecureStorage();
      return await storage.read(key: 'auth_token');
    } catch (e) {
      AppUtils.logError('Error getting auth token', e);
      return null;
    }
  }

  /// Mendapatkan semua domain
  Future<List<Domain>> getDomains() async {
    AppUtils.logInfo('Fetching all domains');
    
    try {
      // Jika menggunakan mock data, langsung kembalikan data mock
      if (_useMockData) {
        AppUtils.logInfo('Using mock data for domains');
        return _getMockDomains();
      }
      
      final headers = await _headers;
      final response = await _client.get(
        Uri.parse('$baseUrl${AppConstants.domainsEndpoint}'),
        headers: headers,
      ).timeout(const Duration(seconds: AppConstants.connectionTimeout));

      if (response.statusCode == 200) {
        AppUtils.logInfo('Successfully fetched domains');
        final Map<String, dynamic> jsonResponse = json.decode(response.body);
        final List<dynamic> domainsData = jsonResponse['data'];
        return domainsData.map((json) => _parseBackendDomain(json)).toList();
      } else {
        AppUtils.logError('Failed to load domains', 'Status code: ${response.statusCode}');
        throw ServerException(
          'Failed to load domains',
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      AppUtils.logWarning('Error fetching domains, using mock data: $e');
      // Fallback ke mock data jika terjadi error
      return _getMockDomains();
    }
  }

  /// Mendapatkan detail domain berdasarkan ID
  Future<Domain> getDomain(String id) async {
    AppUtils.logInfo('Fetching domain with id: $id');
    
    try {
      // Jika menggunakan mock data, langsung kembalikan data mock
      if (_useMockData) {
        AppUtils.logInfo('Using mock data for domain details');
        return _getMockDomains().firstWhere(
          (d) => d.id == id,
          orElse: () => throw ValidationException('Domain not found with id: $id'),
        );
      }
      
      final headers = await _headers;
      final response = await _client.get(
        Uri.parse('$baseUrl${AppConstants.domainsEndpoint}/$id'),
        headers: headers,
      ).timeout(const Duration(seconds: AppConstants.connectionTimeout));

      if (response.statusCode == 200) {
        AppUtils.logInfo('Successfully fetched domain details');
        final Map<String, dynamic> jsonResponse = json.decode(response.body);
        return _parseBackendDomain(jsonResponse['data']);
      } else if (response.statusCode == 404) {
        AppUtils.logError('Domain not found', 'ID: $id');
        throw ValidationException('Domain not found with id: $id');
      } else {
        AppUtils.logError('Failed to load domain details', 'Status code: ${response.statusCode}');
        throw ServerException(
          'Failed to load domain details',
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      if (e is ValidationException) rethrow;
      
      AppUtils.logWarning('Error fetching domain details, using mock data: $e');
      // Fallback ke mock data jika terjadi error
      return _getMockDomains().firstWhere(
        (d) => d.id == id,
        orElse: () => throw ValidationException('Domain not found with id: $id'),
      );
    }
  }

  /// Menambahkan domain baru
  Future<Domain> addDomain(String url) async {
    final cleanUrl = AppUtils.cleanDomainUrl(url);
    AppUtils.logInfo('Adding new domain: $cleanUrl');
    
    // Validasi domain
    if (!AppUtils.isValidDomain(cleanUrl)) {
      AppUtils.logError('Invalid domain format', cleanUrl);
      throw ValidationException('Invalid domain format: $cleanUrl');
    }
    
    try {
      // Jika menggunakan mock data, buat domain mock baru
      if (_useMockData) {
        AppUtils.logInfo('Using mock data for adding domain');
        await Future.delayed(const Duration(milliseconds: 500)); // Simulate network delay
        final newDomain = _createMockDomain(cleanUrl);
        _mockDomains.add(newDomain); // Add to static list
        AppUtils.logInfo('Domain added to mock data: ${newDomain.url}');
        return newDomain;
      }
      
      final headers = await _headers;
      final response = await _client.post(
        Uri.parse('$baseUrl${AppConstants.domainsEndpoint}'),
        headers: headers,
        body: json.encode({
          'url': cleanUrl,
        }),
      ).timeout(const Duration(seconds: AppConstants.connectionTimeout));

      if (response.statusCode == 200 || response.statusCode == 201) {
        AppUtils.logInfo('Successfully added domain');
        final Map<String, dynamic> jsonResponse = json.decode(response.body);
        return _parseBackendDomain(jsonResponse['data']);
      } else {
        AppUtils.logError('Failed to add domain', 'Status code: ${response.statusCode}');
        throw ServerException(
          'Failed to add domain',
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      if (e is ValidationException) rethrow;
      
      AppUtils.logWarning('Error adding domain, creating mock domain: $e');
      // Fallback ke mock data jika terjadi error
      return _createMockDomain(cleanUrl);
    }
  }

  /// Menghapus domain berdasarkan ID
  Future<void> deleteDomain(String id) async {
    AppUtils.logInfo('Deleting domain with id: $id');
    
    try {
      // Jika menggunakan mock data, hapus dari static list
      if (_useMockData) {
        AppUtils.logInfo('Using mock data for deleting domain');
        await Future.delayed(const Duration(milliseconds: 300)); // Simulate network delay
        _mockDomains.removeWhere((domain) => domain.id == id);
        AppUtils.logInfo('Domain deleted from mock data: $id');
        return; // Mock deletion always succeeds
      }
      
      final headers = await _headers;
      final response = await _client.delete(
        Uri.parse('$baseUrl${AppConstants.domainsEndpoint}/$id'),
        headers: headers,
      ).timeout(const Duration(seconds: AppConstants.connectionTimeout));

      if (response.statusCode == 204 || response.statusCode == 200) {
        AppUtils.logInfo('Successfully deleted domain');
        return;
      } else if (response.statusCode == 404) {
        AppUtils.logError('Domain not found for deletion', 'ID: $id');
        throw ValidationException('Domain not found with id: $id');
      } else {
        AppUtils.logError('Failed to delete domain', 'Status code: ${response.statusCode}');
        throw ServerException(
          'Failed to delete domain',
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      if (e is ValidationException) rethrow;
      
      // Untuk demo mode, simulasikan penghapusan berhasil jika terjadi error jaringan
      if (e.toString().contains('Failed host lookup') || 
          e.toString().contains('SocketException') ||
          e.toString().contains('Connection refused')) {
        AppUtils.logWarning('Network unavailable, simulating domain deletion for id: $id');
        await Future.delayed(const Duration(milliseconds: 500)); // Simulate network delay
        return; // Success in demo mode
      }
      
      // Re-throw other types of errors
      AppUtils.logError('Error deleting domain', e);
      rethrow;
    }
  }

  /// Parse domain dari backend JSON
  Domain _parseBackendDomain(Map<String, dynamic> json) {
    try {
      return Domain(
        id: json['id'],
        url: json['url'],
        createdAt: DateTime.parse(json['createdAt']),
        updatedAt: DateTime.parse(json['updatedAt']),
        attackStats: AttackStats(
          ddos: json['attackStats']['ddos'],
          sqlInjection: json['attackStats']['sqlInjection'],
          xss: json['attackStats']['xss'],
        ),
        securityLogs: (json['securityLogs'] as List<dynamic>?)
            ?.map((log) => SecurityLog(
                  timestamp: DateTime.parse(log['timestamp']),
                  message: log['message'],
                  isAlert: log['isAlert'],
                ))
            .toList() ?? [],
        detectedAttacks: (json['detectedAttacks'] as List<dynamic>?)
            ?.map((attack) => DetectedAttack(
                  type: _parseAttackType(attack['type']),
                  status: attack['mitigationStatus'],
                  severity: attack['severity'],
                  detectedAt: DateTime.parse(attack['detectedAt']),
                  resolvedAt: attack['resolvedAt'] != null
                      ? DateTime.parse(attack['resolvedAt'])
                      : null,
                  details: attack['details'],
                ))
            .toList() ?? [],
      );
    } catch (e) {
      AppUtils.logError('Error parsing backend domain data', e);
      throw ValidationException('Invalid backend domain data format');
    }
  }

  /// Parse jenis serangan dari string
  AttackType _parseAttackType(String type) {
    switch (type.toLowerCase()) {
      case 'ddos':
        return AttackType.ddos;
      case 'sqlinjection':
        return AttackType.sqlInjection;
      case 'xss':
        return AttackType.xss;
      default:
        AppUtils.logWarning('Unknown attack type: $type, defaulting to SQL Injection');
        return AttackType.sqlInjection;
    }
  }

  /// Membuat domain mock baru untuk demo
  Domain _createMockDomain(String url) {
    final newId = (_mockDomains.length + 1).toString();
    return Domain(
      id: newId,
      url: url,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      attackStats: const AttackStats(ddos: 0, sqlInjection: 0, xss: 0),
      securityLogs: [
        SecurityLog(
          timestamp: DateTime.now(),
          message: 'Domain successfully added to monitoring system',
          isAlert: false,
        ),
      ],
      detectedAttacks: const [],
    );
  }

  /// Static list untuk menyimpan mock domains yang persisten
  static final List<Domain> _mockDomains = [
    Domain(
      id: '1',
      url: 'trenteknologimobile.com',
      createdAt: DateTime(2025, 5, 1),
      updatedAt: DateTime(2025, 5, 2),
      attackStats: const AttackStats(ddos: 0, sqlInjection: 0, xss: 0),
      securityLogs: [
        SecurityLog(
          timestamp: DateTime(2025, 5, 1, 10, 22, 45),
          message: 'SQL injection attempt detected from 192.168.1.77 on /login',
          isAlert: true,
        ),
        SecurityLog(
          timestamp: DateTime(2025, 5, 1, 10, 22, 46),
          message: 'No threats detected - normal activity',
          isAlert: false,
        ),
        SecurityLog(
          timestamp: DateTime(2025, 5, 1, 10, 22, 43),
          message: 'Clean traffic observed - no suspicious activity',
          isAlert: false,
        ),
      ],
      detectedAttacks: [
        DetectedAttack(
          type: AttackType.sqlInjection,
          status: 'Resolved',
          severity: 'Medium',
          detectedAt: DateTime(2025, 5, 1, 10, 22, 45),
          resolvedAt: DateTime(2025, 5, 1, 10, 23, 30),
          details: 'The system has automatically blocked a malicious IP and isolated affected components to prevent the spread of the attack',
        ),
      ],
    ),
    Domain(
      id: '2',
      url: 'proyekutamainformatika.com',
      createdAt: DateTime(2025, 5, 1),
      updatedAt: DateTime(2025, 5, 1),
      attackStats: const AttackStats(ddos: 0, sqlInjection: 0, xss: 0),
      securityLogs: [
        SecurityLog(
          timestamp: DateTime(2025, 5, 1, 10, 22, 47),
          message: 'No threats detected - normal activity',
          isAlert: false,
        ),
        SecurityLog(
          timestamp: DateTime(2025, 5, 10, 10, 22, 46),
          message: 'No threats detected - normal activity',
          isAlert: false,
        ),
        SecurityLog(
          timestamp: DateTime(2025, 5, 1, 10, 22, 43),
          message: 'Clean traffic observed - no suspicious activity',
          isAlert: false,
        ),
      ],
      detectedAttacks: const [],
    ),
  ];

  /// Data mock untuk demo
  List<Domain> _getMockDomains() {
    return List.from(_mockDomains); // Return copy of static list
  }
}