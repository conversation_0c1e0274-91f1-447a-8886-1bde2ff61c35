import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import 'package:syra_security/features/auth/presentation/widgets/google_sign_in_button.dart';
import 'package:syra_security/features/domains/presentation/providers/domain_provider.dart';
import 'package:syra_security/widgets/syra_logo_widget.dart';

class LoginScreen extends ConsumerWidget {
  const LoginScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authState = ref.watch(authProvider);
    
    // Auto navigate to dashboard if already authenticated
    ref.listen(authProvider, (previous, next) {
      if (next.isAuthenticated) {
        context.go('/dashboard');
      }
    });
    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 24.0),
          child: Column(
            children: [
              // Spacer untuk mendorong konten ke tengah
              const Spacer(flex: 3),
              
              // Logo dan teks di bagian tengah
              const Column(
                children: [
                  // Logo SYRA sesuai gambar yang diberikan
                  SyraLogoWidget(
                    size: 120,
                    backgroundColor: Color(0xFF1565C0),
                    foregroundColor: Colors.white,
                    showShadow: true,
                  ),
                  SizedBox(height: 24),
                  // Teks SYRA
                  Text(
                    "SYRA",
                    style: TextStyle(
                      fontSize: 32,
                      fontWeight: FontWeight.bold,
                      color: Colors.black,
                      letterSpacing: 2.0,
                    ),
                  ),
                  SizedBox(height: 12),
                  // Subtitle
                  Text(
                    "Secure Your Realm Always",
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.grey,
                      fontWeight: FontWeight.w400,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
              
              // Spacer untuk mendorong tombol ke bawah
              const Spacer(flex: 4),
              
              // Tombol sign in di bagian bawah
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: SizedBox(
                  width: double.infinity,
                  child: GoogleSignInButton(
                    onPressed: authState.isLoading ? () {} : () {
                      ref.read(authProvider.notifier).signInWithGoogle().catchError((e) {
                        if (context.mounted) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text('Login failed: $e'),
                              backgroundColor: Colors.red,
                            ),
                          );
                        }
                      });
                    },
                  ),
                ),
              ),
              
              if (authState.isLoading) ...[
                const SizedBox(height: 16),
                const CircularProgressIndicator(),
              ],
               
              // Ruang di bagian paling bawah
              const SizedBox(height: 48),
            ],
          ),
        ),
      ),
    );
  }
}

