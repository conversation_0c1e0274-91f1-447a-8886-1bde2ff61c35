import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:syra_security/core/exceptions.dart';
import 'package:syra_security/core/utils.dart';
import 'package:syra_security/features/domains/data/models/domain_model.dart';

/// Enum untuk tipe event WebSocket
enum WebSocketEventType {
  domainUpdate,
  securityAlert,
  attackDetected,
  statsUpdate,
  connectionStatus,
}

/// Model untuk WebSocket event
class WebSocketEvent {
  final WebSocketEventType type;
  final Map<String, dynamic> data;
  final DateTime timestamp;

  const WebSocketEvent({
    required this.type,
    required this.data,
    required this.timestamp,
  });

  factory WebSocketEvent.fromJson(Map<String, dynamic> json) {
    return WebSocketEvent(
      type: WebSocketEventType.values.firstWhere(
        (e) => e.toString().split('.').last == json['type'],
        orElse: () => WebSocketEventType.connectionStatus,
      ),
      data: json['data'] ?? {},
      timestamp: DateTime.parse(json['timestamp'] ?? DateTime.now().toIso8601String()),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'type': type.toString().split('.').last,
      'data': data,
      'timestamp': timestamp.toIso8601String(),
    };
  }
}

/// Service untuk menangani koneksi WebSocket real-time
class WebSocketService {
  WebSocket? _socket;
  Timer? _heartbeatTimer;
  Timer? _reconnectTimer;
  
  final String baseUrl;
  final Duration heartbeatInterval;
  final Duration reconnectInterval;
  final int maxReconnectAttempts;
  
  int _reconnectAttempts = 0;
  bool _isConnecting = false;
  bool _shouldReconnect = true;

  // Stream controllers
  final StreamController<WebSocketEvent> _eventController = StreamController<WebSocketEvent>.broadcast();
  final StreamController<bool> _connectionController = StreamController<bool>.broadcast();

  // Public streams
  Stream<WebSocketEvent> get events => _eventController.stream;
  Stream<bool> get connectionStatus => _connectionController.stream;

  WebSocketService({
    required this.baseUrl,
    this.heartbeatInterval = const Duration(seconds: 30),
    this.reconnectInterval = const Duration(seconds: 5),
    this.maxReconnectAttempts = 5,
  });

  /// Koneksi ke WebSocket server
  Future<void> connect({String? authToken}) async {
    if (_isConnecting || _socket != null) {
      AppUtils.logWarning('WebSocket already connecting or connected');
      return;
    }

    _isConnecting = true;
    _shouldReconnect = true;

    try {
      AppUtils.logInfo('Connecting to WebSocket: $baseUrl');
      
      // Buat URL WebSocket
      final wsUrl = '${baseUrl.replaceFirst('http', 'ws')}/ws';
      final uri = Uri.parse(wsUrl);
      
      // Headers untuk autentikasi
      final headers = <String, String>{};
      if (authToken != null) {
        headers['Authorization'] = 'Bearer $authToken';
      }

      // Koneksi ke WebSocket
      _socket = await WebSocket.connect(
        uri.toString(),
        headers: headers.isNotEmpty ? headers : null,
      ).timeout(const Duration(seconds: 10));

      AppUtils.logInfo('WebSocket connected successfully');
      _isConnecting = false;
      _reconnectAttempts = 0;
      _connectionController.add(true);

      // Setup listeners
      _setupSocketListeners();
      _startHeartbeat();

    } catch (e) {
      AppUtils.logError('WebSocket connection failed', e);
      _isConnecting = false;
      _connectionController.add(false);
      
      if (_shouldReconnect && _reconnectAttempts < maxReconnectAttempts) {
        _scheduleReconnect();
      } else {
        throw NetworkException('Failed to connect to WebSocket: $e');
      }
    }
  }

  /// Setup listeners untuk WebSocket
  void _setupSocketListeners() {
    _socket?.listen(
      (data) {
        try {
          final jsonData = json.decode(data);
          final event = WebSocketEvent.fromJson(jsonData);
          AppUtils.logInfo('WebSocket event received: ${event.type}');
          _eventController.add(event);
        } catch (e) {
          AppUtils.logError('Error parsing WebSocket message', e);
        }
      },
      onError: (error) {
        AppUtils.logError('WebSocket error', error);
        _handleDisconnection();
      },
      onDone: () {
        AppUtils.logInfo('WebSocket connection closed');
        _handleDisconnection();
      },
    );
  }

  /// Handle disconnection
  void _handleDisconnection() {
    _socket = null;
    _stopHeartbeat();
    _connectionController.add(false);

    if (_shouldReconnect && _reconnectAttempts < maxReconnectAttempts) {
      _scheduleReconnect();
    }
  }

  /// Schedule reconnection
  void _scheduleReconnect() {
    _reconnectAttempts++;
    AppUtils.logInfo('Scheduling WebSocket reconnect attempt $_reconnectAttempts');
    
    _reconnectTimer?.cancel();
    _reconnectTimer = Timer(reconnectInterval, () {
      if (_shouldReconnect) {
        connect();
      }
    });
  }

  /// Start heartbeat untuk menjaga koneksi
  void _startHeartbeat() {
    _heartbeatTimer?.cancel();
    _heartbeatTimer = Timer.periodic(heartbeatInterval, (timer) {
      if (_socket != null) {
        try {
          _socket!.add(json.encode({
            'type': 'ping',
            'timestamp': DateTime.now().toIso8601String(),
          }));
        } catch (e) {
          AppUtils.logError('Error sending heartbeat', e);
          _handleDisconnection();
        }
      }
    });
  }

  /// Stop heartbeat
  void _stopHeartbeat() {
    _heartbeatTimer?.cancel();
    _heartbeatTimer = null;
  }

  /// Send message ke WebSocket
  void sendMessage(Map<String, dynamic> message) {
    if (_socket == null) {
      AppUtils.logWarning('Cannot send message: WebSocket not connected');
      return;
    }

    try {
      final jsonMessage = json.encode(message);
      _socket!.add(jsonMessage);
      AppUtils.logInfo('WebSocket message sent: ${message['type']}');
    } catch (e) {
      AppUtils.logError('Error sending WebSocket message', e);
    }
  }

  /// Subscribe ke domain updates
  void subscribeToDomain(String domainId) {
    sendMessage({
      'type': 'subscribe',
      'data': {
        'domainId': domainId,
      },
      'timestamp': DateTime.now().toIso8601String(),
    });
  }

  /// Unsubscribe dari domain updates
  void unsubscribeFromDomain(String domainId) {
    sendMessage({
      'type': 'unsubscribe',
      'data': {
        'domainId': domainId,
      },
      'timestamp': DateTime.now().toIso8601String(),
    });
  }

  /// Subscribe ke semua domain updates untuk user
  void subscribeToAllDomains() {
    sendMessage({
      'type': 'subscribeAll',
      'timestamp': DateTime.now().toIso8601String(),
    });
  }

  /// Disconnect dari WebSocket
  Future<void> disconnect() async {
    AppUtils.logInfo('Disconnecting WebSocket');
    
    _shouldReconnect = false;
    _stopHeartbeat();
    _reconnectTimer?.cancel();
    
    try {
      await _socket?.close();
    } catch (e) {
      AppUtils.logError('Error closing WebSocket', e);
    }
    
    _socket = null;
    _connectionController.add(false);
  }

  /// Cek status koneksi
  bool get isConnected => _socket != null;

  /// Dispose resources
  void dispose() {
    disconnect();
    _eventController.close();
    _connectionController.close();
  }
}

/// Extension untuk WebSocketEvent
extension WebSocketEventExtension on WebSocketEvent {
  /// Convert ke Domain jika event type adalah domainUpdate
  Domain? toDomain() {
    if (type != WebSocketEventType.domainUpdate) return null;
    
    try {
      return Domain.fromJson(data);
    } catch (e) {
      AppUtils.logError('Error converting WebSocket event to Domain', e);
      return null;
    }
  }

  /// Convert ke SecurityLog jika event type adalah securityAlert
  SecurityLog? toSecurityLog() {
    if (type != WebSocketEventType.securityAlert) return null;
    
    try {
      return SecurityLog.fromJson(data);
    } catch (e) {
      AppUtils.logError('Error converting WebSocket event to SecurityLog', e);
      return null;
    }
  }

  /// Convert ke DetectedAttack jika event type adalah attackDetected
  DetectedAttack? toDetectedAttack() {
    if (type != WebSocketEventType.attackDetected) return null;
    
    try {
      return DetectedAttack.fromJson(data);
    } catch (e) {
      AppUtils.logError('Error converting WebSocket event to DetectedAttack', e);
      return null;
    }
  }

  /// Convert ke AttackStats jika event type adalah statsUpdate
  AttackStats? toAttackStats() {
    if (type != WebSocketEventType.statsUpdate) return null;
    
    try {
      return AttackStats.fromJson(data);
    } catch (e) {
      AppUtils.logError('Error converting WebSocket event to AttackStats', e);
      return null;
    }
  }
}
