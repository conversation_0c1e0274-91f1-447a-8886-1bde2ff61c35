# SYRA Security Backend API

Backend API untuk aplikasi mobile SYRA Security yang men<PERSON> endpoint untuk manajemen domain dan monitoring keamanan.

## 🚀 Quick Start

### Prerequisites
- Node.js (v16 atau lebih baru)
- npm atau yarn

### Installation
```bash
cd backend
npm install
```

### Running the Server
```bash
# Development mode dengan auto-reload
npm run dev

# Production mode
npm start
```

Server akan ber<PERSON><PERSON> di `http://localhost:3000`

## 📋 API Endpoints

### Health Check
- **GET** `/health` - Cek status server

### Domains
- **GET** `/api/domains` - <PERSON>bil semua domain
- **GET** `/api/domains/:id` - Ambil domain berdasarkan ID
- **POST** `/api/domains` - Tambah domain baru
- **DELETE** `/api/domains/:id` - Hapus domain

### Simulation (untuk testing)
- **POST** `/api/domains/:id/simulate-attack` - <PERSON><PERSON><PERSON><PERSON> serangan

## 📊 Data Structure

### Domain Object
```json
{
  "id": "uuid",
  "url": "example.com",
  "createdAt": "2025-05-01T00:00:00.000Z",
  "updatedAt": "2025-05-02T00:00:00.000Z",
  "attackStats": {
    "ddos": 0,
    "sqlInjection": 1,
    "xss": 0
  },
  "securityLogs": [...],
  "detectedAttacks": [...]
}
```

## 🔧 Configuration

Environment variables di `.env`:
- `PORT` - Port server (default: 3000)
- `NODE_ENV` - Environment mode
- `API_BASE_URL` - Base URL untuk API

## 📱 Mobile App Integration

Backend ini dirancang untuk berintegrasi dengan aplikasi Flutter SYRA Security. Pastikan:

1. Backend berjalan di `http://localhost:3000`
2. Mobile app dikonfigurasi untuk menggunakan endpoint yang benar
3. CORS sudah dikonfigurasi untuk menerima request dari mobile app

## 🛡️ Security Features

- Helmet.js untuk security headers
- CORS protection
- Input validation
- Error handling middleware
- Request logging dengan Morgan

## 📝 Sample Data

Backend sudah dilengkapi dengan sample data untuk testing:
- 2 domain contoh (trenteknologimobile.com, proyekutamainformatika.com)
- Security logs sample
- Detected attacks sample

## 🔄 Development

Untuk development, gunakan:
```bash
npm run dev
```

Server akan restart otomatis ketika ada perubahan file.
