# MCP PostgreSQL Configuration
# Copy this file to .env and update with your actual values

# Database Configuration
PG_HOST=localhost
PG_PORT=5432
PG_DATABASE=syra_db
PG_USERNAME=postgres
PG_PASSWORD=postgres

# For production, use a more secure password
# PG_PASSWORD=your_secure_password_here

# MCP Server Configuration
MCP_SERVER_PORT=3000
MCP_LOG_LEVEL=info

# Claude Desktop Config Path (Windows)
# CLAUDE_CONFIG_PATH=%APPDATA%\Claude\claude_desktop_config.json

# Claude Desktop Config Path (macOS)
# CLAUDE_CONFIG_PATH=~/Library/Application Support/Claude/claude_desktop_config.json

# Security Settings
MCP_READ_ONLY=true
MCP_ENABLE_SSL=false

# Connection Pool Settings
PG_POOL_MIN=1
PG_POOL_MAX=10
PG_POOL_IDLE_TIMEOUT=30000