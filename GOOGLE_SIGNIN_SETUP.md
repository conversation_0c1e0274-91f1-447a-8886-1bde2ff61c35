# 🔐 **GOOGLE SIGN-IN SETUP GUIDE**

## 📋 **MASALAH YANG DIPERBAIKI**

1. ✅ **Logo SYRA** - <PERSON><PERSON><PERSON> dengan custom painter yang stylized
2. ✅ **Google Sign-In Button** - Didesain ulang sesuai gambar
3. ✅ **Konfigurasi Google Services** - Ditambahkan file dan plugin yang diperlukan

## 🛠️ **SETUP UNTUK PRODUCTION**

### **1. Buat Project di Google Cloud Console**

1. Buka [Google Cloud Console](https://console.cloud.google.com/)
2. Buat project baru: `syra-security-prod`
3. Enable Google Sign-In API

### **2. Konfigurasi OAuth 2.0**

1. Buka **APIs & Services** > **Credentials**
2. Buat **OAuth 2.0 Client ID** untuk Android:
   - Application type: **Android**
   - Package name: `com.example.syra_security`
   - SHA-1 certificate fingerprint: (lihat langkah berikutnya)

### **3. Dapatkan SHA-1 Fingerprint**

Untuk **Debug** (development):
```bash
cd frontend/android
./gradlew signingReport
```

Untuk **Release** (production):
```bash
keytool -list -v -keystore your-release-key.keystore -alias your-key-alias
```

### **4. Download google-services.json**

1. Download file `google-services.json` dari Firebase Console
2. Replace file yang ada di `frontend/android/app/google-services.json`

### **5. Update Package Name (Opsional)**

Untuk production, ganti package name:

**File: `frontend/android/app/build.gradle.kts`**
```kotlin
defaultConfig {
    applicationId = "com.syrasecurity.app" // Ganti dari com.example.syra_security
    // ...
}
```

**File: `frontend/android/app/src/main/kotlin/com/example/syra_security/MainActivity.kt`**
Pindahkan ke: `frontend/android/app/src/main/kotlin/com/syrasecurity/app/MainActivity.kt`

Dan update package:
```kotlin
package com.syrasecurity.app
```

## 🔧 **TESTING GOOGLE SIGN-IN**

### **1. Build dan Run**
```bash
cd frontend
flutter clean
flutter pub get
flutter run
```

### **2. Troubleshooting**

**Error: "Sign in failed"**
- Pastikan SHA-1 fingerprint benar
- Cek internet connection
- Pastikan Google Services enabled

**Error: "PlatformException"**
- Restart app setelah menambah google-services.json
- Clean dan rebuild project

## 📱 **UI/UX IMPROVEMENTS YANG SUDAH DITERAPKAN**

### **1. Logo SYRA Baru**
- Custom painter dengan huruf "S" yang stylized
- Ukuran lebih besar (120x120)
- Shadow yang lebih halus

### **2. Google Sign-In Button**
- Design sesuai dengan gambar yang diberikan
- Shadow dan border yang tepat
- Responsive touch feedback

### **3. Layout Improvements**
- Spacing yang lebih baik
- Typography yang konsisten
- Color scheme yang sesuai brand

## 🚀 **NEXT STEPS**

1. **Setup Google Cloud Project** dengan credentials yang benar
2. **Test Google Sign-In** dengan SHA-1 fingerprint yang valid
3. **Update package name** untuk production
4. **Add app signing** untuk Google Play Store

## 📞 **SUPPORT**

Jika masih ada masalah dengan Google Sign-In:
1. Pastikan semua langkah setup sudah diikuti
2. Cek log error di console
3. Verifikasi konfigurasi di Google Cloud Console
