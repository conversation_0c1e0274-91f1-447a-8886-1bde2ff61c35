/// <PERSON>las dasar untuk semua exception dalam aplikasi
class AppException implements Exception {
  final String message;
  final String? code;
  final dynamic details;

  AppException(this.message, {this.code, this.details});

  @override
  String toString() {
    return 'AppException: $message${code != null ? ' (Code: $code)' : ''}';
  }
}

/// Exception untuk kesalahan jaringan
class NetworkException extends AppException {
  NetworkException(super.message, {super.code, super.details});
}

/// Exception untuk kesalahan autentikasi
class AuthException extends AppException {
  AuthException(super.message, {super.code, super.details});
}

/// Exception untuk kesalahan autentikasi spesifik
class AuthenticationException extends AuthException {
  AuthenticationException(super.message, {super.code, super.details});
}

/// Exception untuk timeout
class TimeoutException extends NetworkException {
  TimeoutException(super.message, {super.code, super.details});
}

/// Exception untuk cache
class CacheException extends AppException {
  CacheException(super.message, {super.code, super.details});
}

/// Exception untuk permission
class PermissionException extends AppException {
  PermissionException(super.message, {super.code, super.details});
}

/// Exception untuk kesalahan validasi
class ValidationException extends AppException {
  ValidationException(super.message, {super.code, super.details});
}

/// Exception untuk kesalahan server
class ServerException extends AppException {
  final int? statusCode;

  ServerException(super.message, {this.statusCode, super.code, super.details});
      
  @override
  String toString() {
    return 'ServerException: $message${statusCode != null ? ' (Status: $statusCode)' : ''}${code != null ? ' (Code: $code)' : ''}';
  }
}